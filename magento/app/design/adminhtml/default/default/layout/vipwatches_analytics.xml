<?xml version="1.0"?>
<!--
/**
 * Vip-watches Analytics Layout Configuration
 * 
 * This layout file defines the admin interface structure for the Analytics module.
 * It specifies which blocks to load for each controller action and how they
 * should be arranged in the admin interface.
 * 
 * @category   Vipwatches
 * @package    Vipwatches_Analytics
 * <AUTHOR> Development Team
 */
-->
<layout version="0.1.0">
    
    <!-- Default layout for all analytics pages -->
    <vipwatches_analytics_default>
        <reference name="content">
            <!-- Add common CSS and JavaScript for all analytics pages -->
            <block type="core/text" name="vipwatches_analytics_css">
                <action method="setText">
                    <text><![CDATA[
                        <style type="text/css">
                            /* Common styles for all analytics pages */
                            .analytics-module .content-header {
                                margin-bottom: 20px;
                            }
                            
                            .analytics-module .icon-head {
                                background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIgMTRIMTRWMkgyVjE0Wk0zIDNIMTNWMTNIM1YzWiIgZmlsbD0iIzMzMzMzMyIvPgo8cGF0aCBkPSJNNSA2SDdWMTFINVY2WiIgZmlsbD0iIzMzMzMzMyIvPgo8cGF0aCBkPSJNOSA4SDExVjExSDlWOFoiIGZpbGw9IiMzMzMzMzMiLz4KPC9zdmc+');
                                background-repeat: no-repeat;
                                background-position: left center;
                                padding-left: 25px;
                            }
                            
                            .analytics-module .form-button {
                                margin-right: 10px;
                                margin-bottom: 10px;
                            }
                        </style>
                    ]]></text>
                </action>
            </block>
        </reference>
    </vipwatches_analytics_default>
    
    <!-- Dashboard page layout -->
    <adminhtml_vipwatches_analytics_index>
        <update handle="vipwatches_analytics_default"/>
        <reference name="content">
            <block type="vipwatches_analytics/adminhtml_dashboard" name="vipwatches_analytics_dashboard" template="vipwatches/analytics/dashboard.phtml">
                <action method="setHeaderText">
                    <text>Analytics Dashboard</text>
                </action>
            </block>
        </reference>
        <reference name="head">
            <action method="setTitle">
                <title>Vip-watches Analytics Dashboard</title>
            </action>
        </reference>
    </adminhtml_vipwatches_analytics_index>
    
    <!-- Store Information page layout -->
    <adminhtml_vipwatches_analytics_storeinfo>
        <update handle="vipwatches_analytics_default"/>
        <reference name="content">
            <block type="vipwatches_analytics/adminhtml_storeinfo" name="vipwatches_analytics_storeinfo" template="vipwatches/analytics/storeinfo.phtml">
                <action method="setHeaderText">
                    <text>Store Information</text>
                </action>
            </block>
        </reference>
        <reference name="head">
            <action method="setTitle">
                <title>Vip-watches Store Information</title>
            </action>
        </reference>
    </adminhtml_vipwatches_analytics_storeinfo>
    
    <!-- Order Analytics page layout -->
    <adminhtml_vipwatches_analytics_orders>
        <update handle="vipwatches_analytics_default"/>
        <reference name="content">
            <block type="vipwatches_analytics/adminhtml_orders" name="vipwatches_analytics_orders" template="vipwatches/analytics/orders.phtml">
                <action method="setHeaderText">
                    <text>Order Analytics</text>
                </action>
            </block>
        </reference>
        <reference name="head">
            <action method="setTitle">
                <title>Vip-watches Order Analytics</title>
            </action>
        </reference>
    </adminhtml_vipwatches_analytics_orders>
    
    <!-- Sales Reports page layout -->
    <adminhtml_vipwatches_analytics_sales>
        <update handle="vipwatches_analytics_default"/>
        <reference name="content">
            <block type="vipwatches_analytics/adminhtml_sales" name="vipwatches_analytics_sales" template="vipwatches/analytics/sales.phtml">
                <action method="setHeaderText">
                    <text>Sales Reports</text>
                </action>
            </block>
        </reference>
        <reference name="head">
            <action method="setTitle">
                <title>Vip-watches Sales Reports</title>
            </action>
        </reference>
    </adminhtml_vipwatches_analytics_sales>
    
</layout>
