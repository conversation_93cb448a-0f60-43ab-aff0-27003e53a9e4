<?xml version="1.0" encoding="UTF-8"?>
<config>
	<modules>
		<Flagbit_ChangeAttributeSet>
			<active>true</active>
			<codePool>community</codePool>
			<version>1.0.4</version>
		</Flagbit_ChangeAttributeSet>
	</modules>
	
	<global>
		<blocks>
			<adminhtml>
				<rewrite>
					<catalog_product_grid>Flagbit_ChangeAttributeSet_Block_ProductGrid</catalog_product_grid>
				</rewrite>
			</adminhtml>
		</blocks>

		<rewrite>
			<flagbit_form_index>
				<from><![CDATA[#^/{adminhtml}/catalog_product/changeattributeset/.*#]]></from>
				<to>/changeattributeset/</to>
			</flagbit_form_index>			
		</rewrite>
	</global>
	
    <admin>
        <routers>
            <flagbit_changeattributeset>
                <use>admin</use>
                <args>
                    <module>Flagbit_ChangeAttributeSet</module>
                    <frontName>changeattributeset</frontName>
                </args>
            </flagbit_changeattributeset>
        </routers>
    </admin>
</config>
