<config>
    <api2>
        <resource_groups>
            <reclaim translate="title" module="Klaviyo_Reclaim">
                <title>Coupon API</title>
                <sort_order>10</sort_order>
            </reclaim>
        </resource_groups>
        <resources>
            <reclaim translate="title" module="Klaviyo_Reclaim">
                <group>reclaim</group>
                <model>reclaim/api2_coupon</model>
                <title>Coupon Code Auto Generation</title>
                <sort_order>10</sort_order>
                <privileges>
                    <admin>
                        <create>1</create>
                        <retrieve>1</retrieve>
                        <delete>1</delete>
                    </admin>
                </privileges>
                <attributes>
                    <coupon_id>Coupon ID</coupon_id>
                    <code>Code</code>
                    <qty>Quantity</qty>
                    <length>Length</length>
                    <format>Format</format>
                </attributes>
                <routes>
                    <route_collection>
                        <route>/klaviyo/rules/:rule_id/codes</route>
                        <action_type>collection</action_type>
                    </route_collection>
                    <route_entity>
                        <route>/klaviyo/rules/:rule_id/codes/:coupon_id</route>
                        <action_type>entity</action_type>
                    </route_entity>
                </routes>
                <versions>1</versions>
            </reclaim>
            <rules translate="title" module="Klaviyo_Reclaim">
                <group>reclaim</group>
                <model>reclaim/api2_rule</model>
                <title>Fetch Rule Information</title>
                <sort_order>20</sort_order>
                <privileges>
                    <admin>
                        <retrieve>1</retrieve>
                    </admin>
                </privileges>
                <attributes>
                    <rule_id>Rule Id</rule_id>
                    <name>Rule Name</name>
                    <description>Description</description>
                    <from_date>From Date</from_date>
                    <to_date>To Date</to_date>
                    <uses_per_coupon>Uses Per Coupon</uses_per_coupon>
                    <uses_per_customer>Uses Per Customer</uses_per_customer>
                    <simple_action>Apply</simple_action>
                    <discount_amount>Discount Amount</discount_amount>
                </attributes>
                <routes>
                    <route_collection>
                        <route>/klaviyo/rules</route>
                        <action_type>collection</action_type>
                    </route_collection>
                </routes>
                <versions>1</versions>
            </rules>
            <category_root translate="title" module="Klaviyo_Category_Root">
                <group>reclaim</group>
                <model>reclaim/api2_category</model>
                <title>Fetch Category Root for Store View</title>
                <sort_order>30</sort_order>
                <privileges>
                    <admin>
                        <retrieve>1</retrieve>
                    </admin>
                </privileges>
                <attributes>
                    <category_root_id>Category Root Id</category_root_id>
                </attributes>
                <routes>
                    <route_entity>
                        <route>/klaviyo/category_root/:store_view_id</route>
                        <action_type>entity</action_type>
                    </route_entity>
                </routes>
                <versions>1</versions>
            </category_root>
            <category_root translate="title" module="Klaviyo_Category_Root">
                <group>reclaim</group>
                <model>reclaim/api2_category</model>
                <title>Fetch Category Root for All Stores</title>
                <sort_order>40</sort_order>
                <privileges>
                    <admin>
                        <retrieve>1</retrieve>
                    </admin>
                </privileges>
                <attributes>
                    <store_id>Category Root Id</store_id>
                    <category_root_id>Category Root Id</category_root_id>
                </attributes>
                <routes>
                    <route_collection>
                        <route>/klaviyo/category_root</route>
                        <action_type>collection</action_type>
                    </route_collection>
                </routes>
                <versions>1</versions>
            </category_root>
        </resources>
    </api2>
</config>
