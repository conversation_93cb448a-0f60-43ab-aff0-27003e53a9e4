<?xml version="1.0"?>
<config>
    <modules>
        <Klaviyo_Reclaim>
            <version>1.7.9</version>
        </Klaviyo_Reclaim>
    </modules>
    <global>
        <events>
            <newsletter_subscriber_save_before>
                <observers>
                    <klaviyo_subscribe_observer>
                        <type>singleton</type>
                        <class>Klaviyo_Reclaim_Model_Observer</class>
                        <method>syncSubscriber</method>
                    </klaviyo_subscribe_observer>
                </observers>
            </newsletter_subscriber_save_before>
            <newsletter_subscriber_delete_after>
                <observers>
                    <klaviyo_subscribe_delete_observer>
                        <type>singleton</type>
                        <class>Klaviyo_Reclaim_Model_Observer</class>
                        <method>syncSubscriberDelete</method>
                    </klaviyo_subscribe_delete_observer>
                </observers>
            </newsletter_subscriber_delete_after>
        </events>
        <models>
            <klaviyo_reclaim>
                <class>Klaviyo_Reclaim_Model</class>
                <resourceModel>reclaim_mysql4</resourceModel>
            </klaviyo_reclaim>
            <reclaim_mysql4>
                <class>Klaviyo_Reclaim_Model_Mysql4</class>
                <entities>
                    <checkout>
                        <table>klaviyo_reclaim_checkout</table>
                    </checkout>
                </entities>
            </reclaim_mysql4>
            <reclaim>
                <class>Klaviyo_Reclaim_Model</class>
            </reclaim>
        </models>
        <helpers>
            <klaviyo_reclaim>
                <class>Klaviyo_Reclaim_Helper</class>
            </klaviyo_reclaim>
        </helpers>
        <blocks>
            <klaviyo_reclaim>
                <class>Klaviyo_Reclaim_Block</class>
            </klaviyo_reclaim>
        </blocks>
        <resources>
            <klaviyo_reclaim_setup>
                <setup>
                    <module>Klaviyo_Reclaim</module>
                    <class>Mage_Core_Model_Resource_Setup</class>
                </setup>
            </klaviyo_reclaim_setup>
        </resources>
    </global>
    <admin>
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <Klaviyo_Reclaim after="Mage_Adminhtml">Klaviyo_Reclaim</Klaviyo_Reclaim>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>
    <adminhtml>
        <layout>
            <updates>
                <klaviyo_reclaim>
                    <file>klaviyoreclaim.xml</file>
                </klaviyo_reclaim>
            </updates>
        </layout>
    </adminhtml>
    <frontend>
        <routers>
            <klaviyo_reclaim>
                <use>standard</use>
                <args>
                    <module>Klaviyo_Reclaim</module>
                    <frontName>reclaim</frontName>
                </args>
            </klaviyo_reclaim>
        </routers>
        <layout>
            <updates>
                <klaviyo_reclaim>
                    <file>klaviyoreclaim.xml</file>
                </klaviyo_reclaim>
            </updates>
        </layout>
    </frontend>
    <default>
        <reclaim>
            <general>
                <enabled>0</enabled>
                <optin_setting>1</optin_setting>
            </general>
        </reclaim>
    </default>
    <crontab>
        <jobs>
            <klaviyo_track_quotes>
                <schedule>
                    <cron_expr>*/15 * * * *</cron_expr>
                </schedule>
                <run>
                    <model>klaviyo_reclaim/observer::trackQuotes</model>
                </run>
            </klaviyo_track_quotes>
        </jobs>
    </crontab>
</config>
