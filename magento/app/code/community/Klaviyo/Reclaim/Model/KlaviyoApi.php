<?php
/**
 * @category    Klaviyo
 * @package     Klaviyo_Reclaim
 * @copyright   Copyright (c) 2013 Klaviyo Inc. (http://www.klaviyo.com)
 */


/**
 * Klaviyo Api
 *
 * @category   Klaviyo
 * @package    Klaviyo_Reclaim
 * <AUTHOR> Team <<EMAIL>>
 */
class Klaviyo_Reclaim_Model_KlaviyoApi
{
    const API_MEMBERS = '/members';
    const API_SUBSCRIBE = '/subscribe';
    const API_LISTS = 'lists';
    const API_LIST = 'list/';
    const HTTP_POST = 'POST';
    const HTTP_GET = 'GET';
    const HTTP_DELETE = 'DELETE';
    const EMAIL_KEY = 'email';
    const EMAILS_KEY = 'emails';
    const PROFILES_KEY = 'profiles';

    var $api_version = '2';
    var $api_url;

    var $error_message;
    var $error_code;

    /**
     * Default to a 300 second timeout on server calls
     */
    var $timeout = 300;

    /**
     * Default to a 8K chunk size
     */
    var $chunk_size = 8192;

    /**
     * Cache the user api_key so we only have to log in once per client instantiation
     */
    var $api_key;

    /**
     * @var array Request params storage
     */
    public $request_params = array();

    /**
     * Connect to the Klaviyo API for a given list.
     *
     * @param string $apikey Your Klaviyo apikey
     * @param string $secure Whether or not this should use a secure connection
     */
    function __construct($api_key, $secure=true) {
        $this->api_key = $api_key;
        $this->secure = $secure;
        $this->api_base_url = 'https://a.klaviyo.com/api/v' . $this->api_version . '/';
    }

    function setTimeout($seconds) {
        if (is_int($seconds)) {
            $this->timeout = $seconds;
            return true;
        }
    }

    function getTimeout() {
        return $this->timeout;
    }

    function lists() {
        $params = array();
        return $this->callServer(self::HTTP_GET, self::API_LISTS, $params);
    }

    function listMembers($list_id, $email) {
        $params = array(self::EMAILS_KEY => array($email));
        return $this->callServer(self::HTTP_GET, self::API_LIST . $list_id . self::API_MEMBERS, $params);
    }

    function listMembersAdd($list_id, $email) {
        $params = array(self::PROFILES_KEY => array(self::EMAIL_KEY => $email));
        return $this->callServer(self::HTTP_POST, self::API_LIST . $list_id . self::API_MEMBERS, $params);
    }

    function listMembersDelete($list_id, $email) {
        $params = array(self::EMAILS_KEY => array($email));
        return $this->callServer(self::HTTP_DELETE, self::API_LIST . $list_id . self::API_MEMBERS, $params);
    }

    function listSubscriberAdd($list_id, $email) {
        $params = array(self::PROFILES_KEY => array(self::EMAIL_KEY => $email));
        return $this->callServer(self::HTTP_POST, self::API_LIST . $list_id . self::API_SUBSCRIBE, $params);
    }

    function listSubscriberDelete($list_id, $email) {
        $params = array(self::EMAILS_KEY => array($email));
        return $this->callServer(self::HTTP_DELETE, self::API_LIST . $list_id . self::API_SUBSCRIBE, $params);
    }

    function callServer($method, $path, $params) {

        $this->request_params = $params;

        $params['api_key'] = $this->api_key;

        $this->errorMessage = '';
        $this->errorCode = '';

        $client = new Zend_Http_Client($this->api_base_url . $path);
        $client->setMethod($method);
    
        $client->setHeaders(array(
            "Content-Type" => "application/json",
            "Accept" => "application/json"
        ));
        $client->setRawData(json_encode($params));

        try {
            $response = $client->request();
            if (!$response->isSuccessful()) {
                $this->error_message = $response->getBody();
                $this->error_code = $response->getStatus();
                return false;
            }
        } catch (Exception $ex) {
            Mage::logException($ex);
            return $ex->getMessage();
        }

        $json_response = Zend_Json::decode($response->getBody());

        if(is_array($json_response) && isset($json_response['errors'])) {
            $this->error_message = $json_response['errors'];
            $this->error_code = $response->getStatus();
            return false;

        }

        return $json_response;
    }

}