<?php
/* Coupon REST API
*
* @category    Klaviyo
* @package     Klaviyo_Reclaim
* <AUTHOR> Team <<EMAIL>>
*/
class Klaviyo_Reclaim_Model_Api2_Coupon_Rest_Admin_V1 extends Klaviyo_Reclaim_Model_Api2_Coupon
{
    const COUPON_CREATE_SUCCESS = 'Coupon created';
    const COUPON_CODE_GENERATE_FAILURE = 'Unable to generate Coupon code. Please check your parameters and try again.';

    /**
     * Generate one or more coupon codes using the Generate Coupons rule provided.
     *   This must be a _multiCreate so we can return messages, _creates cannot return messages.
     *   In order for this endpoint to be hit, params must not be an associative array.
     *
     * Expected parameters are...
     *       qty: (int) number of coupon codes to instruct Magento to generate
     *    length: (int) length of each generated coupon code
     *    format: (string) coupon code format, one of 'alphanum', 'alpha', and 'num'
     *
     * @return string
     */
    protected function _multiCreate($data)
    {
        $ruleId = $this->getRequest()->getParam('rule_id');
        $rule = $this->_loadSalesRule($ruleId);

        $couponData = $data[0];
        $couponData['rule_id'] = $ruleId;

        $couponGenerator = $rule->getCouponMassGenerator();

        // validate coupon attributes
        if (!$couponGenerator->validateData($couponData)) {
            $this->_critical(Mage::helper('salesrule')->__('Coupon API: Invalid parameters provided.'),
                Mage_Api2_Model_Server::HTTP_BAD_REQUEST);
        }

        $couponGenerator->setData($couponData);

        if ($couponGenerator->getMaxAttempts()) {
            $maxGenerateAttempts = $couponGenerator->getMaxAttempts();
        } else {
            $maxGenerateAttempts = $couponGenerator::MAX_GENERATE_ATTEMPTS;
        }

        $now = $couponGenerator->getResource()->formatDate(
            Mage::getSingleton('core/date')->gmtTimestamp()
        );

        $coupon = Mage::getModel('salesrule/coupon');

        for ($i = 0; $i < $couponData['qty']; $i++) {
            $attempt = 0;
            $breakOut = false;
            do {
                if ($attempt >= $maxGenerateAttempts) {
                    // Report this failure for all coupons that still need to be created,
                    //   and flag to end trying to generation coupon codes.
                    for ($failedCouponIndex = 0; $failedCouponIndex < $couponData['qty'] - $i; $failedCouponIndex++) {
                        $this->getResponse()->addMessage(self::COUPON_CODE_GENERATE_FAILURE, 0);
                    }
                    $breakOut = true;
                } else {
                  $code = $couponGenerator->generateCode();
                  $attempt++;
                }
            } while ($couponGenerator->getResource()->exists($code));

            if ($breakOut) {
                break;
            }

            $expirationDate = $couponGenerator->getToDate();
            if ($expirationDate instanceof Zend_Date) {
                $expirationDate = $expirationDate->toString(Varien_Date::DATETIME_INTERNAL_FORMAT);
            }
            //Fixes bug with usage limit in a way to read the Usage Limit from the Rule itself
            $usageLimit = $rule->getUsesPerCoupon() ? $rule->getUsesPerCoupon() : 1;
            $coupon->setId(null)
                ->setRuleId($ruleId)
                ->setUsageLimit($usageLimit)
                ->setUsagePerCustomer($rule->getUsesPerCustomer())
                ->setExpirationDate($expirationDate)
                ->setCreatedAt($now)
                ->setType(Mage_SalesRule_Helper_Coupon::COUPON_TYPE_SPECIFIC_AUTOGENERATED)
                ->setCode($code)
                ->save();

            $this->getResponse()->addMessage(
                self::COUPON_CREATE_SUCCESS, $coupon['code'],
                array('id' => $coupon->getId()),
                Mage_Api2_Model_Response::MESSAGE_TYPE_SUCCESS
            );
        }
    }

    /**
     * Delete a Coupon associated with a rule.
     *
     * @return void
     */
    protected function _delete()
    {
        $ruleId = $this->getRequest()->getParam('rule_id');
        $rule = $this->_loadSalesRule($ruleId);

        $couponId = $this->getRequest()->getParam('coupon_id');
        $coupon = Mage::getModel('salesrule/coupon')->load($couponId);

        // perform validation to ensure the coupon we want to delete is associated with the rule in mind
        if ($coupon->getRuleId() != $rule->getId()) {
            $this->_critical(Mage::helper('salesrule/coupon')->__('Coupon API: Invalid parameters provided, rule not associated with coupon.'),
                Mage_Api2_Model_Server::HTTP_BAD_REQUEST);
        }

        try {
            $coupon->delete();
        } catch (Mage_Core_Exception $e) {
            $this->_critical($e->getMessage(), Mage_Api2_Model_Server::HTTP_INTERNAL_ERROR);
        } catch (Exception $e) {
            $this->_critical(self::RESOURCE_INTERNAL_ERROR);
        }

        echo 1;
    }

    /**
     * Retrieve list of coupon codes.
     *
     * @return array
     */
    protected function _retrieveCollection()
    {
        $ruleId = $this->getRequest()->getParam('rule_id');
        $rule = $this->_loadSalesRule($ruleId);

        $couponCollection = Mage::getResourceModel('salesrule/coupon_collection');
        $couponCollection->addRuleToFilter($rule);

        $this->_applyCollectionModifiers($couponCollection);
        $data = $couponCollection->load()->toArray();

        return $data['items'];
    }

    /**
     * Load sales rule by ID.
     *
     * @param int $ruleId
     * @return Mage_SalesRule_Model_Rule
     */
    protected function _loadSalesRule($ruleId)
    {
        if (!$ruleId) {
            $this->_critical(Mage::helper('salesrule')
                ->__('Rule ID is required.'), Mage_Api2_Model_Server::HTTP_BAD_REQUEST);
        }
        $rule = Mage::getModel('salesrule/rule')->load($ruleId);
        if (!$rule->getId()) {
            $this->_critical(Mage::helper('salesrule')
                ->__('Rule was not found.'), Mage_Api2_Model_Server::HTTP_NOT_FOUND);
        }
        return $rule;
    }
}
