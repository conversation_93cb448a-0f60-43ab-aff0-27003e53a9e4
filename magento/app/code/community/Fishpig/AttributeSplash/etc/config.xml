<?xml version="1.0"?>
<config>
	<modules>
		<Fishpig_AttributeSplash>
			<version>3.3.8.15</version>
		</Fishpig_AttributeSplash>
	</modules>
	<global>
		<helpers>
			<attributeSplash>
				<class>Fishpig_AttributeSplash_Helper</class>
			</attributeSplash>		
		</helpers>
		<blocks>
			<attributeSplash>
				<class>Fishpig_AttributeSplash_Block</class>
			</attributeSplash>
		</blocks>
		<models>
			<attributeSplash>
				<class>Fishpig_AttributeSplash_Model</class>
				<resourceModel>attributeSplash_resource</resourceModel>
			</attributeSplash>
			<attributeSplash_resource>
				<class>Fishpig_AttributeSplash_Model_Resource</class>
				<entities>
					<group><table>attributesplash_group</table></group>
					<group_store><table>attributesplash_group_store</table></group_store>
					<group_index><table>attributesplash_group_index</table></group_index>
					<page><table>attributesplash_page</table></page>
					<page_store><table>attributesplash_page_store</table></page_store>
					<page_index><table>attributesplash_page_index</table></page_index>
				</entities>
			</attributeSplash_resource>
		</models>
		<resources>
			<attributeSplash_setup>
				<setup>
					<module>Fishpig_AttributeSplash</module>
				</setup>
				<connection>
					<use>core_setup</use>
				</connection>
			</attributeSplash_setup>
			<attributeSplash_read>
				<connection>
					<use>core_read</use>
				</connection>
			</attributeSplash_read>
			<attributeSplash_write>
				<connection>
					<use>core_write</use>
				</connection>
			</attributeSplash_write>
		</resources>
		<events>
			<controller_front_init_routers>
				<observers>
					<attributeSplash>
						<class>Fishpig_AttributeSplash_Controller_Router</class>
						<method>initControllerRouters</method>
					</attributeSplash>
				</observers>
			</controller_front_init_routers>
			<catalog_prepare_price_select>
				<observers>
					<attributeSplash>
						<class>attributeSplash/observer</class>
						<method>prepareCatalogPriceSelectObserver</method>
					</attributeSplash>
				</observers>
			</catalog_prepare_price_select>
			<fseo_layered_navigation_match_entity>
				<observers>
					<attributeSplash>
						<class>attributeSplash/observer</class>
						<method>fseoLayeredNavigationMatchEntityObserver</method>
					</attributeSplash>
				</observers>			
			</fseo_layered_navigation_match_entity>
		</events>
		<index>
			<indexer>
				<attributeSplash>
					<model>attributeSplash/indexer</model>
				</attributeSplash>
			</indexer>
		</index>
	</global>
	<admin>
		<routers>
			<adminhtml>
				<args>
					<modules>
						<attributeSplash before="Mage_Adminhtml">Fishpig_AttributeSplash_Adminhtml</attributeSplash>
					</modules>
				</args>
			</adminhtml>
		</routers>
	</admin>
	<adminhtml>
		<layout>
			<updates>
				<attributeSplash>
					<file>attribute-splash.xml</file>
				</attributeSplash>
			</updates>
		</layout>
	</adminhtml>
	<frontend>
		<routers>
			<attributeSplash>
				<use>standard</use>
				<args>
					<module>Fishpig_AttributeSplash</module>
					<frontName>splash</frontName>
				</args>
			</attributeSplash>
		</routers>
		<layout>
			<updates>
				<attributeSplash>
					<file>attribute-splash.xml</file>
				</attributeSplash>
			</updates>
		</layout>
		<events>
			<page_block_html_topmenu_gethtml_before>
				<observers>
					<attributeSplash>
						<type>singleton</type>
						<class>attributeSplash/observer</class>
						<method>injectTopmenuLinksObserver</method>
					</attributeSplash>
				</observers>
			</page_block_html_topmenu_gethtml_before>
		</events>
	</frontend>
	<default>
		<attributeSplash>
			<page>
				<column_count>4</column_count>
			</page>
			<group>
				<column_count>4</column_count>
			</group>
			<navigation>
				<enabled>1</enabled>
			</navigation>
			<seo>
				<url_suffix>.html</url_suffix>
			</seo>
			<missing_addon>
				<quickcreate>1</quickcreate>
				<xmlsitemap>1</xmlsitemap>
			</missing_addon>
		</attributeSplash>
	</default>
</config>