<?xml version="1.0"?>
<config>
	<tabs>
		<fishpig translate="label" module="attributeSplash">
			<label>FishPig</label>
			<sort_order>200</sort_order>
		</fishpig>
	</tabs>
    <sections>
        <attributeSplash translate="label" module="attributeSplash">
            <label>Attribute Splash Pages</label>
            <tab>fishpig</tab>
            <sort_order>210</sort_order>
            <show_in_default>1</show_in_default>
            <show_in_website>1</show_in_website>
            <show_in_store>1</show_in_store>
            <groups>
            	<page>
            		<label>Pages</label>
            		<sort_order>6</sort_order>
            		<show_in_default>1</show_in_default>
            		<show_in_website>1</show_in_website>
            		<show_in_store>1</show_in_store>
            		<fields>
            			<template>
            				<label>Page Layout</label>
            				<frontend_type>select</frontend_type>
            				<source_model>attributeSplash/system_config_source_layout</source_model>
            				<sort_order>1</sort_order>
            				<show_in_default>1</show_in_default>
            				<show_in_website>1</show_in_website>
            				<show_in_store>1</show_in_store>
            			</template>
            			<column_count>
            				<label>Column Count</label>
            				<comment>Number of Splash pages per row</comment>
            				<frontend_type>text</frontend_type>
            				<sort_order>6</sort_order>
            				<show_in_default>1</show_in_default>
            				<show_in_website>1</show_in_website>
            				<show_in_store>1</show_in_store>
            				<validate>validate-number required-entry</validate>
            			</column_count>
            			<include_group_url_key>
            				<label>Add Group URL Key to URL</label>
            				<frontend_type>select</frontend_type>
            				<source_model>adminhtml/system_config_source_yesno</source_model>
            				<sort_order>106</sort_order>
            				<show_in_default>1</show_in_default>
            				<show_in_website>1</show_in_website>
            				<show_in_store>1</show_in_store>
            			</include_group_url_key>
            		</fields>
            	</page>
				<group>
            		<label>Groups</label>
					<sort_order>11</sort_order>
            		<show_in_default>1</show_in_default>
            		<show_in_website>1</show_in_website>
            		<show_in_store>1</show_in_store>
            		<fields>
            			<template>
            				<label>Page Layout</label>
            				<frontend_type>select</frontend_type>
            				<source_model>attributeSplash/system_config_source_layout</source_model>
            				<sort_order>1</sort_order>
            				<show_in_default>1</show_in_default>
            				<show_in_website>1</show_in_website>
            				<show_in_store>1</show_in_store>
            			</template>
            			<column_count>
            				<label>Column Count</label>
            				<comment>Number of Splash pages per row</comment>
            				<frontend_type>text</frontend_type>
            				<sort_order>6</sort_order>
            				<show_in_default>1</show_in_default>
            				<show_in_website>1</show_in_website>
            				<show_in_store>1</show_in_store>
            				<validate>validate-number required-entry</validate>
            			</column_count>
            		</fields>
				</group>
            	<seo>
            		<label>Search Engine Optimizations</label>
            		<sort_order>14</sort_order>
            		<show_in_default>1</show_in_default>
            		<show_in_website>1</show_in_website>
            		<show_in_store>1</show_in_store>
            		<fields>
            			<url_suffix>
            				<label>URL Suffix</label>
            				<frontend_type>text</frontend_type>
            				<sort_order>6</sort_order>
            				<show_in_default>1</show_in_default>
            			</url_suffix>
            		</fields>
            	</seo>
            	<navigation>
            		<label>Navigation</label>
            		<sort_order>19</sort_order>
            		<show_in_default>1</show_in_default>
            		<show_in_website>1</show_in_website>
            		<show_in_store>1</show_in_store>
            		<fields>
            			<enabled>
            				<label>Inject Links in Top Navigation</label>
            				<comment>Depends on your theme</comment>
            				<frontend_type>select</frontend_type>
            				<source_model>adminhtml/system_config_source_yesno</source_model>
            				<sort_order>1</sort_order>
            				<show_in_default>1</show_in_default>
            				<show_in_website>1</show_in_website>
            				<show_in_store>1</show_in_store>
            			</enabled>
            		</fields>
            	</navigation>
            	<custom_fields>
            		<label>Custom Fields</label>
            		<sort_order>999</sort_order>
            		<show_in_default>1</show_in_default>
            		<show_in_website>1</show_in_website>
            		<show_in_store>1</show_in_store>
            		<fields>
            			<page>
            				<label>Page Fields</label>
            				<comment>Enter the name of each field on a new line<br/>(eg. some_field)</comment>
            				<frontend_type>editor</frontend_type>
            				<sort_order>1</sort_order>
            				<show_in_default>1</show_in_default>
            				<show_in_website>1</show_in_website>
            				<show_in_store>1</show_in_store>
            			</page>
            		</fields>
            	</custom_fields>
            </groups>
        </attributeSplash>
    </sections>
</config>