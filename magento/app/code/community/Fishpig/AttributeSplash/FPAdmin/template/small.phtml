<?php
/**
 *
 */
?>
<?php if ($extensions = $this->getSelectedExtensions()): ?>
<div id="<?php echo $this->getId() ?>">
	<ul>
		<?php foreach($extensions as $extension): ?>
			<li class="item<?php if ($this->isLast($extension)): ?> last<?php endif; ?>">
				<div class="pad">
					<a href="<?php echo $this->getTrackedUrl($extension, $this->getModule(), 'image') ?>" class="image" target="_blank">
						<img src="<?php echo $this->getImageUrl($extension) ?>" alt="" />
					</a>
					<h2>
						<a href="<?php echo $this->getTrackedUrl($extension, $this->getModule(), 'title') ?>" target="_blank">
							<strong><?php echo $this->escapeHtml($this->getTitle($extension)) ?></strong> <span><?php echo $this->escapeHtml($this->getSubTitle($extension)) ?></span>
						</a>
					</h2>
					<p><?php echo $this->getShortDefinition($extension) ?> <a href="<?php echo $this->getTrackedUrl($extension, $this->getModule(), 'view-more') ?>" target="_blank">More info</a></p>
				</div>
			</li>
		<?php endforeach; ?>
	</ul>
</div>
<script type="text/javascript">decorateList($('<?php echo $this->getId() ?>').select('ul').first());</script>
<style type="text/css">
#<?php echo $this->getId() ?> { margin: 0; }
#<?php echo $this->getId() ?> ul { height: 1%; overflow: hidden;  }
#<?php echo $this->getId() ?> li.item  { margin: 0 0 10px; width: 50%; float: left; }
#<?php echo $this->getId() ?> li.item .pad {padding: 1%; height: 1%; overflow: hidden; border:1px solid #ddd; margin: 0 5px 0 0; }
#<?php echo $this->getId() ?> li.item.even .pad { margin: 0 0 0 5px; }
#<?php echo $this->getId() ?> a.image { float: left; display: block; margin-right: 10px; }
#<?php echo $this->getId() ?> a.image img { max-height: 60px; display: block; }
#<?php echo $this->getId() ?> h2 { font-size: 1.4em; font-family: Tahoma, Verdana, Arial; line-height: 1em; margin: 5px 0 2px; }
#<?php echo $this->getId() ?> h2 a { color: #000; text-decoration: none; }
#<?php echo $this->getId() ?> p { margin-bottom: 0; }
</style>
<?php endif; ?>