<?xml version="1.0" encoding="UTF-8"?>

<!--
/**
 * Inchoo is not affiliated with or in any way responsible for this code.
 *
 * Commercial support is available directly from the [extension author](http://www.techytalk.info/contact/).
 *
 * @category Marko-M
 * @package SocialConnect
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) <PERSON><PERSON> (http://www.techytalk.info)
 * @license http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 */
-->

<config>
    <sections>
        <customer>
            <groups>
                <inchoo_socialconnect_google translate="label" module="inchoo_socialconnect">
                    <label>Social Connect Google Options</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>120</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <enabled translate="label">
                            <label>Enabled</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </enabled>
                        <client_id translate="label" module="inchoo_socialconnect">
                            <label>Google API Client ID</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends><enabled>1</enabled></depends>
                            <validate>required-entry</validate>
                        </client_id>
                        <client_secret translate="label" module="inchoo_socialconnect">
                            <label>Google API Client Secret</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends><enabled>1</enabled></depends>
                            <validate>required-entry</validate>
                        </client_secret>
                        <links translate="label" module="inchoo_socialconnect">
                            <label>Links</label>
                            <frontend_type>label</frontend_type>
                            <frontend_model>inchoo_socialconnect/google_adminhtml_system_config_form_field_links</frontend_model>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </links>
                        <origin translate="label" module="inchoo_socialconnect">
                            <label>JavaScript Origins</label>
                            <frontend_type>label</frontend_type>
                            <frontend_model>inchoo_socialconnect/google_adminhtml_system_config_form_field_origins</frontend_model>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </origin>
                        <redirect translate="label" module="inchoo_socialconnect">
                            <label>Redirect URIs</label>
                            <frontend_type>label</frontend_type>
                            <frontend_model>inchoo_socialconnect/google_adminhtml_system_config_form_field_redirects</frontend_model>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </redirect>
                    </fields>
                </inchoo_socialconnect_google>
                <inchoo_socialconnect_facebook translate="label" module="inchoo_socialconnect">
                    <label>Social Connect Facebook Options</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>130</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <enabled translate="label">
                            <label>Enabled</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </enabled>
                        <client_id translate="label" module="inchoo_socialconnect">
                            <label>Facebook App ID</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends><enabled>1</enabled></depends>
                            <validate>required-entry</validate>
                        </client_id>
                        <client_secret translate="label" module="inchoo_socialconnect">
                            <label>Facebook App Secret</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends><enabled>1</enabled></depends>
                            <validate>required-entry</validate>
                        </client_secret>
                        <links translate="label" module="inchoo_socialconnect">
                            <label>Links</label>
                            <frontend_type>label</frontend_type>
                            <frontend_model>inchoo_socialconnect/facebook_adminhtml_system_config_form_field_links</frontend_model>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </links>
                        <origin translate="label" module="inchoo_socialconnect">
                            <label>App Domains</label>
                            <frontend_type>label</frontend_type>
                            <frontend_model>inchoo_socialconnect/facebook_adminhtml_system_config_form_field_origins</frontend_model>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </origin>
                        <redirect translate="label" module="inchoo_socialconnect">
                            <label>Site URL</label>
                            <frontend_type>label</frontend_type>
                            <frontend_model>inchoo_socialconnect/facebook_adminhtml_system_config_form_field_redirects</frontend_model>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </redirect>
                    </fields>
                </inchoo_socialconnect_facebook>
                <inchoo_socialconnect_twitter translate="label" module="inchoo_socialconnect">
                    <label>Social Connect Twitter Options</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>140</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <enabled translate="label">
                            <label>Enabled</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </enabled>
                        <client_id translate="label" module="inchoo_socialconnect">
                            <label>Consumer Key</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends><enabled>1</enabled></depends>
                            <validate>required-entry</validate>
                        </client_id>
                        <client_secret translate="label" module="inchoo_socialconnect">
                            <label>Consumer Secret</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends><enabled>1</enabled></depends>
                            <validate>required-entry</validate>
                        </client_secret>
                        <links translate="label" module="inchoo_socialconnect">
                            <label>Links</label>
                            <frontend_type>label</frontend_type>
                            <frontend_model>inchoo_socialconnect/twitter_adminhtml_system_config_form_field_links</frontend_model>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </links>
                        <origin translate="label" module="inchoo_socialconnect">
                            <label>Website</label>
                            <frontend_type>label</frontend_type>
                            <frontend_model>inchoo_socialconnect/twitter_adminhtml_system_config_form_field_origins</frontend_model>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </origin>
                        <redirect translate="label" module="inchoo_socialconnect">
                            <label>Callback URL</label>
                            <frontend_type>label</frontend_type>
                            <frontend_model>inchoo_socialconnect/twitter_adminhtml_system_config_form_field_redirects</frontend_model>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </redirect>
                    </fields>
                </inchoo_socialconnect_twitter>
                <inchoo_socialconnect_linkedin translate="label" module="inchoo_socialconnect">
                    <label>Social Connect LinkedIn Options</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>150</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <enabled translate="label">
                            <label>Enabled</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </enabled>
                        <client_id translate="label" module="inchoo_socialconnect">
                            <label>LinkedIn API Key</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends><enabled>1</enabled></depends>
                            <validate>required-entry</validate>
                        </client_id>
                        <client_secret translate="label" module="inchoo_socialconnect">
                            <label>LinkedIn Secret Key</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <depends><enabled>1</enabled></depends>
                            <validate>required-entry</validate>
                        </client_secret>
                        <links translate="label" module="inchoo_socialconnect">
                            <label>Links</label>
                            <frontend_type>label</frontend_type>
                            <frontend_model>inchoo_socialconnect/linkedin_adminhtml_system_config_form_field_links</frontend_model>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </links>
                        <origin translate="label" module="inchoo_socialconnect">
                            <label>Website URL</label>
                            <frontend_type>label</frontend_type>
                            <frontend_model>inchoo_socialconnect/linkedin_adminhtml_system_config_form_field_origins</frontend_model>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </origin>
                        <redirect translate="label" module="inchoo_socialconnect">
                            <label>OAuth 2.0 Redirect URLs</label>
                            <frontend_type>label</frontend_type>
                            <frontend_model>inchoo_socialconnect/linkedin_adminhtml_system_config_form_field_redirects</frontend_model>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </redirect>
                    </fields>
                </inchoo_socialconnect_linkedin>
            </groups>
        </customer>
    </sections>
</config>
