<?xml version="1.0" encoding="UTF-8"?>

<!--
/**
 * Inchoo is not affiliated with or in any way responsible for this code.
 *
 * Commercial support is available directly from the [extension author](http://www.techytalk.info/contact/).
 *
 * @category Marko-M
 * @package SocialConnect
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) <PERSON><PERSON> (http://www.techytalk.info)
 * @license http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 */
-->

<config>
    <modules>
        <Inchoo_SocialConnect>
            <version>0.3.8</version>
        </Inchoo_SocialConnect>
    </modules>
    <default>
        <customer>
            <inchoo_socialconnect_google>
                <enabled>0</enabled>
            </inchoo_socialconnect_google>
            <inchoo_socialconnect_facebook>
                <enabled>0</enabled>
            </inchoo_socialconnect_facebook>
            <inchoo_socialconnect_twitter>
                <enabled>0</enabled>
            </inchoo_socialconnect_twitter>
            <inchoo_socialconnect_linkedin>
                <enabled>0</enabled>
            </inchoo_socialconnect_linkedin>
        </customer>
    </default>
    <frontend>
        <translate>
            <modules>
                <Inchoo_SocialConnect>
                     <files>
                        <default>Inchoo_SocialConnect.csv</default>
                     </files>
                </Inchoo_SocialConnect>
            </modules>
        </translate>
        <layout>
            <updates>
                <inchoo_socialconnect>
                    <file>inchoo_socialconnect.xml</file>
                </inchoo_socialconnect>
            </updates>
        </layout>
        <routers>
            <inchoo_socialconnect>
                <use>standard</use>
                <args>
                    <module>Inchoo_SocialConnect</module>
                    <frontName>socialconnect</frontName>
                </args>
            </inchoo_socialconnect>
        </routers>
    </frontend>
    <global>
        <models>
            <inchoo_socialconnect>
                <class>Inchoo_SocialConnect_Model</class>
            </inchoo_socialconnect>
        </models>
        <blocks>
            <inchoo_socialconnect>
                <class>Inchoo_SocialConnect_Block</class>
            </inchoo_socialconnect>
        </blocks>
        <helpers>
            <inchoo_socialconnect>
                <class>Inchoo_SocialConnect_Helper</class>
            </inchoo_socialconnect>
        </helpers>
        <resources>
            <inchoo_socialconnect_setup>
                <setup>
                    <module>Inchoo_SocialConnect</module>
                    <class>Inchoo_SocialConnect_Model_Resource_Setup</class>
                </setup>
            </inchoo_socialconnect_setup>
        </resources>
    </global>
</config>
