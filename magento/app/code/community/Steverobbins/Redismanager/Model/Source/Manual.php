<?php
/**
 * Redis Management Module
 *
 * PHP Version 5
 *
 * @category  Steverobbins
 * @package   Stevero<PERSON>ins_Redismanager
 * <AUTHOR> <<EMAIL>>
 * @copyright 2014 <PERSON>
 * @license   http://creativecommons.org/licenses/by/3.0/deed.en_US Creative Commons Attribution 3.0 Unported License
 * @link      https://github.com/steverobbins/Magento-Redismanager
 */

/**
 * Source model for serialize manual redis host configuration
 *
 * @category  Steverobbins
 * @package   Steverobbins_Redismanager
 * <AUTHOR> <<EMAIL>>
 * @copyright 2014 <PERSON>
 * @license   http://creativecommons.org/licenses/by/3.0/deed.en_US Creative Commons Attribution 3.0 Unported License
 * @link      https://github.com/steverobbins/Magento-Redismanager
 */
class Steverobbins_Redismanager_Model_Source_Manual extends Mage_Adminhtml_Model_System_Config_Backend_Serialized_Array
{
    /**
     * Event prefix for observers
     *
     * @var string
     */
    protected $_eventPrefix = 'redismanager_manual';
}
