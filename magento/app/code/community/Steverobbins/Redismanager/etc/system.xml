<?xml version="1.0"?>
<!--
/**
 * Redis Management Module
 * 
 * @category  Steverobbins
 * @package   Steve<PERSON><PERSON>ins_Redismanager
 * <AUTHOR> <<EMAIL>>
 * @copyright 2014 <PERSON>
 * @license   http://creativecommons.org/licenses/by/3.0/deed.en_US Creative Commons Attribution 3.0 Unported License
 * @link      https://github.com/steverobbins/Magento-Redismanager
 */
-->
<config>
    <sections>
        <redismanager translate="label" module="redismanager">
            <label>Redis Management</label>
            <tab>advanced</tab>
            <frontend_type>text</frontend_type>
            <sort_order>920</sort_order>
            <show_in_default>1</show_in_default>
            <show_in_website>0</show_in_website>
            <show_in_store>0</show_in_store>
            <groups>
                <settings translate="label">
                    <label>Settings</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>10</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>0</show_in_website>
                    <show_in_store>0</show_in_store>
                    <fields>
                        <auto translate="label comment">
                            <label>Automatically detect Redis services</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment>If No, use manual configuration below</comment>
                        </auto>
                        <manual translate="label comment">
                            <label>Manual Configuration</label>
                            <frontend_model>redismanager/adminhtml_system_config_form_field_manual</frontend_model>
                            <backend_model>redismanager/source_manual</backend_model>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment>When using the synchronized flushes and Cm_RedisSession, it is recommended that the Cm_RedisSession database is not listed here so that no sessions are lost.</comment>
                        </manual>
                        <syncflush translate="label comment">
                            <label>Synchronize with Magento cache flushes</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment>Flush all specified Redis databases whenever Magento fires an adminhtml_cache_flush_system OR adminhtml_cache_flush_all observer event.</comment>
                        </syncflush>
                    </fields>
                </settings>
            </groups>
        </redismanager>
    </sections>
</config>
