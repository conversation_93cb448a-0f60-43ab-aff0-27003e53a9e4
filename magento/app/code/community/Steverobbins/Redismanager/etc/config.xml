<?xml version="1.0"?>
<!--
/**
 * Redis Management Module
 * 
 * @category  Steverobbins
 * @package   Steve<PERSON><PERSON>ins_Redismanager
 * <AUTHOR> <<EMAIL>>
 * @copyright 2014 <PERSON>
 * @license   http://creativecommons.org/licenses/by/3.0/deed.en_US Creative Commons Attribution 3.0 Unported License
 * @link      https://github.com/steverobbins/Magento-Redismanager
 */
-->
<config>
    <modules>
        <Steverobbins_Redismanager>
            <version>1.4.5</version>
        </Steverobbins_Redismanager>
    </modules>
    <global>
        <blocks>
            <redismanager>
                <class>Steve<PERSON>bbins_Redismanager_Block</class>
            </redismanager>
        </blocks>
        <helpers>
            <redismanager>
                <class>Steverobbins_Redismanager_Helper</class>
            </redismanager>
        </helpers>
        <models>
            <redismanager>
                <class>Steverobbins_Redismanager_Model</class>
            </redismanager>
        </models>
    </global>
    <admin>
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <Steverobbins_Redismanager before="Mage_Adminhtml">Steverobbins_Redismanager_Adminhtml</Steverobbins_Redismanager>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>
    <adminhtml>
        <layout>
            <updates>
                <redismanager>
                    <file>steverobbins/redismanager.xml</file>
                </redismanager>
            </updates>
        </layout>
        <translate>
            <modules>
                <Steverobbins_Redismanager>
                    <files>
                        <default>Steverobbins_Redismanager.csv</default>
                    </files>
                </Steverobbins_Redismanager>
            </modules>
        </translate>
        <events>
            <adminhtml_cache_flush_all>
                <observers>
                    <redismanager>
                        <type>singleton</type>
                        <class>redismanager/observer</class>
                        <method>cacheFlushAll</method>
                    </redismanager>
                </observers>
            </adminhtml_cache_flush_all>
            <adminhtml_cache_flush_system>
                <observers>
                    <redismanager>
                        <type>singleton</type>
                        <class>redismanager/observer</class>
                        <method>cacheFlushSystem</method>
                    </redismanager>
                </observers>
            </adminhtml_cache_flush_system>
        </events>
    </adminhtml>
    <default>
        <redismanager>
            <settings>
                <auto>1</auto>
                <syncflush>0</syncflush>
            </settings>
        </redismanager>
    </default>
</config>
