<?php
/**
 * Default helper
 *
 * @package Stenik_Sync
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_Sync_Helper_Data extends Mage_Core_Helper_Abstract
{

    public function factoryCatalogProduct()
    {
        $sourceClass = (string) Mage::getConfig()->getNode('stenik_sync/catalog_product/source/class');
        $source = Mage::getModel($sourceClass);
        $sync = Mage::getModel('stenik_sync/catalog_product');
        $sync->setSource($source);
        return $sync;
    }

}
