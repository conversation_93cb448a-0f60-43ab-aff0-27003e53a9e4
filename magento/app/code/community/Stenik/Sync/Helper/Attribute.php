<?php
/**
 * Helper to handle attribute options operations
 *
 * Handle getting of attribute options and creating new ones
 *
 * @package Stenik_Sync
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_Sync_Helper_Attribute extends Mage_Core_Helper_Abstract
{

    protected $_attributes = array();

    protected $_attributeOptions = array();

    public function getOptions($attributeCode)
    {
        return $this->_getOptionsWithModels($attributeCode);
        // return $this->_getOptionsWithApi($attributeCode);   // problem with caching of options
    }

    /**
     * Return array of attribute option ids and values
     *
     * Method uses magento models to retrieve data
     *
     * @param  string $attributeCode AttributeCode
     * @return array                Array with key => value (option value => option label)
     */
    protected function _getOptionsWithModels($attributeCode)
    {
        if (array_key_exists($attributeCode, $this->_attributeOptions)) {
            return $this->_attributeOptions[$attributeCode];
        }

        $this->_attributeOptions[$attributeCode] = array();

        // Disabled because of problems with empty getSource();
        // $attribute = Mage::getModel('eav/entity_attribute')->loadByCode('catalog_product', $attributeCode);
        $attribute = Mage::getResourceModel('catalog/product')->getAttribute($attributeCode);
        $attributeSource = $attribute->getSource();

        $options = $attributeSource->getAllOptions(false);
        foreach ($options as $option) {
            $id = $option['value'];
            $label = $option['label'];
            $this->_attributeOptions[$attributeCode][$id] = $label;
        }
        return $this->_attributeOptions[$attributeCode];
    }

    /**
     * Return array of attribute option ids and values
     *
     * Method uses magento api models to retrieve data
     *
     * @param  string $attributeCode AttributeCode
     * @return array                Array with key => value (option value => option label)
     */
    protected function _getOptionsWithApi($attributeCode)
    {
        if (array_key_exists($attributeCode, $this->_attributeOptions))
            return $this->_attributeOptions[$attributeCode];

        $this->_attributeOptions[$attributeCode] = array();

        $api = Mage::getModel('catalog/product_attribute_api');
        $options = $api->options($attributeCode);
        foreach ($options as $option) {
            $id = $option['value'];
            $label = $option['label'];
            $this->_attributeOptions[$attributeCode][$id] = $label;
        }
        return $this->_attributeOptions[$attributeCode];
    }

    /**
     * Return option by id
     * @param  string $attributeCode AttributeCode
     * @param  int $id            ID of attribute option
     * @return string                Option Label
     */
    public function getOptionById($attributeCode, $id)
    {
        $options = $this->getOptions($attributeCode);
        if (array_key_exists($id, $options))
            return $options[$id];
    }

    /**
     * Return option id by label
     * @param  string $attributeCode AttributeCode
     * @param  string $label         Attribute Label
     * @return int                Attribute ID
     */
    public function getOptionByLabel($attributeCode, $label)
    {
        $options = $this->getOptions($attributeCode);
        foreach ($options as $key => $value) {
            if ($label == $value)
                return $key;
        }
        return false;
    }

    /**
     * Create attribute option with given label
     * @param  string $attributeCode AttributeCode
     * @param  string $value         Attribute Label
     * @return int                Attribute ID
     */
    public function createOption($attributeCode, $value)
    {
        if (!strlen($value))
            return false;

        $this->_clearOptionsCache($attributeCode);

        $api = Mage::getModel('catalog/product_attribute_api');
        $data = array(
            'label' => array(array('store_id' => 0, 'value' => $value)),
            'order' => 0,
            'is_default'    =>  0
        );
        $api->addOption($attributeCode, $data);
        return $this->getOptionByLabel($attributeCode, $value);
    }

    /**
     * Clear the options cache used internally in this helper
     * @param  string $attributeCode AttributeCode
     * @return void                [description]
     */
    protected function _clearOptionsCache($attributeCode)
    {
        $this->_attributeOptions = array();
        if (array_key_exists($attributeCode, $this->_attributeOptions)) {
            unset($this->_attributeOptions[$attributeCode]);
        }
    }

    /**
     * Retrieve attribute by code
     *
     * @param  string $attributeCode
     * @param  string $entityType
     * @return boolean|Mage_Eav_Model_Entity_Attribute
     */
    public function getAttributeByCode($attributeCode, $entityType = Mage_Catalog_Model_Product::ENTITY)
    {
        if (isset($this->_attributes[$attributeCode])) {
            return $this->_attributes[$attributeCode];
        }
        $attribute = Mage::getModel('eav/entity_attribute')->loadByCode($entityType, $attributeCode);

        if ($attribute && $attribute->getId()) {
            $this->_attributes[$attributeCode] = $attribute;
            return $attribute;
        }

        $this->_attributes[$attributeCode] = false;

        return false;
    }

    /**
     * Retrieve attribute ID by code
     *
     * @param  string $attributeCode
     * @param  string $entityType
     * @return boolean|integer
     */
    public function getAttributeIdByCode($attributeCode, $entityType = Mage_Catalog_Model_Product::ENTITY)
    {
        $attribute = $this->getAttributeByCode($attributeCode, $entityType);

        if ($attribute && $attribute->getId()) {
            return $attribute->getId();
        }

        return false;
    }

    /**
     * Check if attribute uses source
     *
     * @param  string $attributeCode
     * @param  string $entityType
     * @return boolean
     */
    public function attributeUsesSource($attributeCode, $entityType = Mage_Catalog_Model_Product::ENTITY)
    {
        $attribute = $this->getAttributeByCode($attributeCode, $entityType);
        return $attribute && $attribute->usesSource();
    }

    /**
     * Check if attribute uses source
     *
     * @param  string $attributeCode
     * @param  string $entityType
     * @return boolean
     */
    public function attributeUsesDefaultSource($attributeCode, $entityType = Mage_Catalog_Model_Product::ENTITY)
    {
        $attribute = $this->getAttributeByCode($attributeCode, $entityType);
        return  $attribute &&
                $attribute->usesSource() &&
                (
                    !$attribute->getSourceModel() ||
                   $attribute->getSourceModel() == Mage::getResourceSingleton('catalog/product')->getDefaultAttributeSourceModel()
                )
        ;
    }

    /**
     * Check if attribute is select
     *
     * @param  string $attributeCode
     * @param  string $entityType
     * @return boolean
     */
    public function attributeIsSelect($attributeCode, $entityType = Mage_Catalog_Model_Product::ENTITY)
    {
        $attribute = $this->getAttributeByCode($attributeCode, $entityType);

        if ($attribute->getFrontendInput() == 'select') {
            return true;
        }

        return false;
    }

    /**
     * Check if attribute is multiselect
     *
     * @param  string $attributeCode
     * @param  string $entityType
     * @return boolean
     */
    public function attributeIsMultiselect($attributeCode, $entityType = Mage_Catalog_Model_Product::ENTITY)
    {
        $attribute = $this->getAttributeByCode($attributeCode, $entityType);

        if ($attribute->getFrontendInput() == 'multiselect') {
            return true;
        }

        return false;
    }
}