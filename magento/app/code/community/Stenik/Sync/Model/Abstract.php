<?php
/**
 * Sync abstract model
 *
 * @package Stenik_Sync
 * <AUTHOR> Magento Team <<EMAIL>>
 */

abstract class Stenik_Sync_Model_Abstract extends Mage_Core_Model_Abstract
{
    /**
     * Model name of the sync
     *
     * @var string
     */
    protected $_syncType = null;

    protected $_source = null;

    protected $_sourceClass = null;

    /**
     * Init.
     *
     * @return void
     */
    protected function _construct()
    {
        parent::_construct();
        $this->setSyncResource('stenik_sync');
    }

    /**
     * [setSyncResource description]
     *
     * @param  [type] $resource [description]
     * @return self
     */
    final public function setSyncResource($resource)
    {
        $this->_resource = null;
        $this->_init($resource.'/'.$this->_syncType);
        return $this;
    }

    /**
     * Retrieve source
     *
     * @param  Stenik_Sync_Model_Interface_Interface $source
     * @return self
     */
    public function setSource(Stenik_Sync_Model_Interface_Interface $source)
    {
        $this->_source = $source;
        return $this;
    }

    /**
     * Retrieve source
     *
     * @return Stenik_Sync_Model_Interface_Interface
     */
    final public function getSource()
    {
        if ($this->_source === null) {
            Mage::throwException('Source model is not defined.');
        }

        return $this->_source;
    }
}
