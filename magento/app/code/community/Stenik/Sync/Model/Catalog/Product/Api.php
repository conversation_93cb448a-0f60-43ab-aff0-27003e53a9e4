<?php
/**
 * @package  Stenik_Sync
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_Sync_Model_Catalog_Product_Api extends Mage_Catalog_Model_Product_Api
{
    /**
     *  Set additional data before product saved
     *
     *  @param    Mage_Catalog_Model_Product $product
     *  @param    array $productData
     *  @return   object
     */
    protected function _prepareDataForSave($product, $productData)
    {
        parent::_prepareDataForSave($product, $productData);

        if (isset($productData['group_price']) && is_array($productData['group_price'])) {
            $groupPrices = Mage::getModel('stenik_sync/catalog_product_api_resource')
                ->prepareGroupPrices($product, $productData['group_price']);
            $product->setData('group_price', $groupPrices);
        }
    }
}