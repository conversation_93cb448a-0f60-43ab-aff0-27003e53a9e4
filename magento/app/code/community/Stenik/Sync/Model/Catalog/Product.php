<?php
/**
 * @package  Stenik_Sync
 * <AUTHOR> Magento Team <<EMAIL>>
 */

/**
 * @method this setUsePagination(boolean $flag)
 * @method boolan|null getUsePagination()
 * @method this setPageSize(integer $pageSize)
 * @method integer|null getPageSize()
 */
class Stenik_Sync_Model_Catalog_Product extends Stenik_Sync_Model_Abstract
{
    protected $_syncType = 'catalog_product';

    /**
     * Array with default data for creating a product
     *
     * @var array
     */
    protected $_defaultProductData = null;

    public function syncSingle(Mage_Catalog_Model_Product $product)
    {
        // echo __METHOD__."\n";
        $item = $this->getSource()->getItem($product);
        if ($item) {
            $this->getResource()->update($product, $item);
        }
    }

    public function syncMultiple()
    {
        return $this;
    }

    public function syncSince(Zend_Date $since = null)
    {
        echo __METHOD__."\n";

        $page = 1;
        $pageCount = 1;

        if ($this->getUsePagination() && method_exists($this->getSource(), 'preparePagination')) {
            if ($this->getPageSize()) {
                $this->getSource()->setPageSize($this->getPageSize());
            }

            $this->getSource()->preparePagination($since);
            $pageCount = max((int) $this->getSource()->getPageCount(), 1);
        }

        do {
            $this->getSource()->setPage($page);
            $result = $this->getSource()->getCollection($since);

            $i = 0; $total = count($result);
            foreach ($result as $item) {
                echo sprintf("%d/%d, Page %d/%d. ID: %s\tSKU:%s\tTYPE:%s\n", $i++, $total, $page, $pageCount, $item->getMagentoId(), $item->getMagentoSku(), $item->getTypeId());

                if ($item->getMagentoId()) {
                    $magentoId = $item->getMagentoId();

                    if ($magentoId == 'getIdBySku' && $item->getMagentoSku()) {
                        $magentoId = Mage::getResourceModel('catalog/product')->getIdBySku($item->getMagentoSku());
                    }

                    if ($magentoId) {
                        try {
                            $this->getResource()->update($magentoId, $item);
                        } catch (Exception $e) {
                            echo sprintf("\tException: %s\n", $e->getMessage());
                        }
                    }
                } else {
                    echo "Create Product ".$item->getMagentoSku()."\n";
                    try {
                        $productId = $this->getResource()->create($item);
                    } catch (Exception $e) {
                        echo sprintf("\tException: %s\n", $e->getMessage());
                    }
                }
            }
            $page++;
        } while($page <= $pageCount);

    }

    public function syncAll()
    {
        return $this->syncSince();
    }
}