<?php
/**
 * @package  Stenik_Sync
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_Sync_Model_Catalog_Product_Api_Resource extends Mage_Catalog_Model_Api_Resource
{
    /**
     *  Prepare group prices for save
     *
     *  @param      Mage_Catalog_Model_Product $product
     *  @param      array $groupPrices
     *  @return     array
     */
    public function prepareGroupPrices($product, $groupPrices = null)
    {
        if (!is_array($groupPrices)) {
            return null;
        }

        $updateValue = array();

        foreach ($groupPrices as $groupPrice1) {
            $groupPrice = is_array($groupPrice1) ? $groupPrice1 : unserialize($groupPrice1);

            if (!is_array($groupPrice)
                || !isset($groupPrice["cust_group"])
                || !isset($groupPrice["price"])) {
                $this->_fault('data_invalid', Mage::helper('catalog')->__('Invalid Group Prices'));
            }

            if (!isset($groupPrice["website_id"]) || $groupPrice["website_id"] == 'all') {
                $groupPrice["website_id"] = 0;
            } else {
                try {
                    $groupPrice["website_id"] = Mage::app()->getWebsite($groupPrice["website"])->getId();
                } catch (Mage_Core_Exception $e) {
                    $groupPrice["website_id"] = 0;
                }
            }

            if (intval($groupPrice["website_id"]) > 0 && !in_array($groupPrice["website_id"], $product->getWebsiteIds())) {
                $this->_fault('data_invalid', Mage::helper('catalog')->__('Invalid group prices. The product is not associated to the requested website.'));
            }

            if (!isset($groupPrice["cust_group"])) {
                $groupPrice["cust_group"] = '0';
            }

            if ($groupPrice["cust_group"] == 'all') {
                $groupPrice["cust_group"] = '0';
            }

            $updateValue[] = array(
                'website_id' => $groupPrice["website_id"],
                'cust_group' => $groupPrice["cust_group"],
                'price'      => isset($groupPrice["price"]) ? $groupPrice["price"] : null,
                'delete'     => isset($groupPrice["delete"]) ? $groupPrice["delete"] : null
            );

        }

        return $updateValue;
    }

}