<?php
/**
 * Dry updater
 *
 * @package Stenik_Sync
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_Sync_Model_Resource_Dry_Catalog_Product extends Mage_Core_Model_Resource_Abstract
{

    protected $_attributesByValue = array('manufacturer', 'measurement'); // TODO - must not be hardcoded here!

    /**
     * Resource initialization
     *
     * @return void
     */
    protected function _construct()
    {
    }

    /**
     * Retrieve connection for read data
     */
    protected function _getReadAdapter()
    {
        return Mage::getSingleton('core/resource')->getConnection('stenik_sync_read');
    }

    /**
     * Retrieve connection for write data
     */
    protected function _getWriteAdapter()
    {
        return Mage::getSingleton('core/resource')->getConnection('stenik_sync_write');
    }

    /**
     * Create product
     *
     * @param  array|Varien_Object $data
     * @return integer product ID
     */
    public function create($data)
    {
        if (is_array($data)) {
            $data = new Varien_Object($data);
        } else if (!($data instanceof Varien_Object)) {
            trigger_error(sprintf('%s() expects parameter 1 to be array or Varien_Object, %s given.', __FUNCTION__, gettype($data)), E_USER_WARNING);
            return;
        }

        $skipAttributes = array('id', 'sku', 'magento_configurable_attribute', 'magento_parent_sku');
        $stockAttributes = array('is_in_stock', 'qty', 'use_config_backorders', 'backorders');
        $requiredAttributes = array('magento_sku', 'type_id', 'attribute_set_id', 'name', 'tax_class_id', 'price', 'description', 'short_description', 'status', 'visibility');
        foreach ($requiredAttributes as $attributeCode) {
            if (!$data->getData($attributeCode))
                Mage::throwException(sprintf('Missing data for attribute %s', $attributeCode));
        }
        if ($data->getTypeId() == 'configurable' && !$data->getMagentoConfigurableAttribute()) {
            Mage::throwException('Can not create configurable product without configurable attributes');
        }

        // Create default product data
        $productData = $this->_getDefaultProductData();
        $productData = array_merge($productData, $data->getData());

        $helperAttribute = Mage::helper('stenik_sync/attribute');

        foreach ($data->getData() as $attributeCode => $value) {

            // already copied to productData
            if (in_array($attributeCode, $requiredAttributes))
                continue;

            // unset because will be daded as additional_attributes
            unset($productData[$attributeCode]);

            // if attribute should be skipped - system attributes for the sync module
            if (in_array($attributeCode, $skipAttributes))
                continue;

            // handle stock attributes separately
            if (in_array($attributeCode, $stockAttributes)) {
                $productData['stock_data'][$attributeCode] = $value;
                continue;
            }

            if (in_array($attributeCode, $this->_attributesByValue) && $helperAttribute->attributeUsesSource($attributeCode))  {
                // If attribute uses source and its value is given by label, then check it and create a new one if necessary
                $attributeOptionId = $helperAttribute->getOptionByLabel($attributeCode, $value);
                if (!$attributeOptionId) {
                    $attributeOptionId = $helperAttribute->createOption($attributeCode, $value);
                }

                if ($attributeOptionId) {
                    $productData['additional_attributes']['single_data'][$attributeCode] = $attributeOptionId;
                }
            } else {
                $productData['additional_attributes']['single_data'][$attributeCode] = $value;
            }

        }

        print_r($productData);
        return $id;
    }

    /**
     * Retrieve product's default data
     *
     * @return array
     */
    protected function _getDefaultProductData()
    {
        if (is_null($this->_defaultProductData) || !is_array($this->_defaultProductData)) {
            $this->_defaultProductData = array();
            $this->_defaultProductData['status'] = Mage_Catalog_Model_Product_Status::STATUS_ENABLED;
            $this->_defaultProductData['visibility'] = Mage_Catalog_Model_Product_Visibility::VISIBILITY_NOT_VISIBLE;
            $this->_defaultProductData['tax_class_id'] = 1;
            $websites = Mage::app()->getWebsites(true);
            $this->_defaultProductData['websites'] = array_keys($websites);
        }
        return $this->_defaultProductData;
    }

    /**
     * Update product
     *
     * @param  Mage_Catalog_Model_Product $product
     * @param  array|Varien_Object $data
     * @return self
     */
    public function update(Mage_Catalog_Model_Product $product, $data)
    {
        if (is_array($data)) {
            $data = new Varien_Object($data);
        } else if (!($data instanceof Varien_Object)) {
            trigger_error(sprintf('%s() expects parameter 1 to be array or Varien_Object, %s given.', __FUNCTION__, gettype($data)), E_USER_WARNING);
            return;
        }

        $stockData = $this->_extractStockData($data);

        $this->_prepareData($data);


        if (count($data->getData())) {
            $attributesToUpdate = $data->getData();
            /**
             * When updating product these attributes should not be set!
             */
            unset($attributesToUpdate['magento_parent_sku']);
            unset($attributesToUpdate['magento_configurable_attribute']);
            unset($attributesToUpdate['type_id']);
            unset($attributesToUpdate['attribute_set_id']);
            unset($attributesToUpdate['force_update']);
            unset($attributesToUpdate['category_ids']);

            $helperAttribute = Mage::helper('stenik_sync/attribute');

            /**
             * Handle update by value - for attributes with source and configured in _attributesByValue
             */
            foreach ($attributesToUpdate as $attributeCode => $value) {
                if (in_array($attributeCode, $this->_attributesByValue) && $helperAttribute->attributeUsesSource($attributeCode)) {
                    // If attribute uses source and its value is given by label, then check it and create a new one if necessary
                    $attributeOptionId = $helperAttribute->getOptionByLabel($attributeCode, $value);
                    if (!$attributeOptionId) {
                        $attributeOptionId = $helperAttribute->createOption($attributeCode, $value);
                    }
                    if ($attributeOptionId) {
                        $attributesToUpdate[$attributeCode] = $attributeOptionId;
                    }
                }
            }

            echo "Updating attributes with values:\n";
            print_r($attributesToUpdate);

        }

        if (count($stockData->getData())) {
            echo "Updating stock\n";
            print_r($stockData->getData());
        }


        if ($data->getMagentoParentSku()) {
            // Try to get the configurable product by sku
            $configurableProduct = Mage::getModel('catalog/product')->loadByAttribute('sku', $data->getMagentoParentSku());
            if (!$configurableProduct || !$configurableProduct->getId())
                continue;

            // Get configurable product current children
            $childenIds = Mage::getResourceSingleton('catalog/product_type_configurable')->getChildrenIds($configurableProduct->getId());
            $currentIds = array_values($childenIds[0]);
            // merge the new product id with current children
            $allIds = array_unique(array_merge($currentIds,array($product->getId())));
            // save product association
        }

        echo "\n\n";
        return $this;
    }

    /**
     * Prepare data
     *
     * @param  Varien_Object $data
     * @return void
     */
    protected function _prepareData(Varien_Object $data)
    {
        $data->unsetData('id');
        $data->unsetData('sku');
        $data->unsetData('magento_id');
        $data->unsetData('magento_sku');
    }

    /**
     * Extract stock data
     *
     * @param  Varien_Object $data
     * @return Varien_Object
     */
    protected function _extractStockData(Varien_Object $data)
    {
        $result = new Varien_Object();
        $keys = array('qty', 'is_in_stock', 'use_config_backorders', 'backorders');
        foreach ($keys as $key) {
            if (!is_null($data->getData($key, null))) {
                $result->setData($key, $data->getData($key));
                $data->unsetData($key);
            }
        }
        return $result;
    }
}
