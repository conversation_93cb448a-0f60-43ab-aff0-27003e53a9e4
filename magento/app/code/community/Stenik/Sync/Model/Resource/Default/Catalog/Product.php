<?php
/**
 * Default updater
 *
 * @package Stenik_Sync
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_Sync_Model_Resource_Default_Catalog_Product extends Mage_Core_Model_Resource_Abstract
{
    /**
     * Product API
     *
     * @var null|Stenik_Sync_Model_Catalog_Product_Api
     */
    protected $_productApi = null;

    /**
     * Loaded products
     *
     * @var null|array
     */
    protected $_products = array();

    /**
     * Resource initialization
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_productApi = Mage::getSingleton('stenik_sync/catalog_product_api');
    }

    /**
     * Retrieve connection for read data
     */
    protected function _getReadAdapter()
    {
        return Mage::getSingleton('core/resource')->getConnection('stenik_sync_read');
    }

    /**
     * Retrieve connection for write data
     */
    protected function _getWriteAdapter()
    {
        return Mage::getSingleton('core/resource')->getConnection('stenik_sync_write');
    }

    /**
     * Create product
     *
     * @param  array|Varien_Object $data
     * @return integer product ID
     */
    public function create($data)
    {
        if (is_array($data)) {
            $data = new Varien_Object($data);
        } else if (!($data instanceof Varien_Object)) {
            trigger_error(sprintf('%s() expects parameter 1 to be array or Varien_Object, %s given.', __FUNCTION__, gettype($data)), E_USER_WARNING);
            return;
        }

        $skipAttributes = array('id', 'sku', 'magento_configurable_attribute', 'magento_parent_sku', 'category_ids', 'up_sell_ids', 'cross_sell_ids', 'related_ids', 'gallery');
        $stockAttributes = array('is_in_stock', 'qty', 'use_config_backorders', 'backorders');
        $requiredAttributes = array('magento_sku', 'type_id', 'attribute_set_id', 'name', 'tax_class_id', 'price', 'description', /*'short_description',*/ 'status', 'visibility');
        foreach ($requiredAttributes as $attributeCode) {
            if (!$data->hasData($attributeCode)) {
                Mage::throwException(sprintf('Missing data for attribute %s', $attributeCode));
            }
        }
        if ($data->getTypeId() == 'configurable' && !$data->getMagentoConfigurableAttribute()) {
            Mage::throwException('Can not create configurable product without configurable attributes');
        }

        // Create default product data
        $productData = $this->_getDefaultProductData();
        $productData = array_merge($productData, $data->getData());

        $helperAttribute = Mage::helper('stenik_sync/attribute');

        foreach ($data->getData() as $attributeCode => $value) {

            // already copied to productData
            if (in_array($attributeCode, $requiredAttributes)) {
                continue;
            }

            // unset because will be daded as additional_attributes
            unset($productData[$attributeCode]);

            // if attribute should be skipped - system attributes for the sync module
            if (in_array($attributeCode, $skipAttributes)) {
                continue;
            }

            // handle stock attributes separately
            if (in_array($attributeCode, $stockAttributes)) {
                $productData['stock_data'][$attributeCode] = $value;
                continue;
            }

            if ($helperAttribute->attributeUsesDefaultSource($attributeCode) && $helperAttribute->attributeIsSelect($attributeCode)) {
                // If attribute uses source and its value is given by label, then check it and create a new one if necessary
                $attributeOptionId = $helperAttribute->getOptionByLabel($attributeCode, $value);
                if (!$attributeOptionId) {
                    $attributeOptionId = $helperAttribute->createOption($attributeCode, $value);
                }

                if ($attributeOptionId) {
                    $productData['additional_attributes']['single_data'][$attributeCode] = $attributeOptionId;
                }
            } else if ($helperAttribute->attributeUsesDefaultSource($attributeCode) && $helperAttribute->attributeIsMultiselect($attributeCode)) {
                // If attribute uses source and its value is given by label, then check it and create a new one if necessary
                if (!is_array($value)) {
                    $value = array($value);
                }

                // get or create options ids for each value
                foreach ($value as $singleValue) {
                    $attributeOptionId = $helperAttribute->getOptionByLabel($attributeCode, $singleValue);
                    if (!$attributeOptionId) {
                        $attributeOptionId = $helperAttribute->createOption($attributeCode, $singleValue);
                    }
                    if ($attributeOptionId) {
                        $productData['additional_attributes']['multi_data'][$attributeCode][] = $attributeOptionId;
                    }
                }
            } else {
                $productData['additional_attributes']['single_data'][$attributeCode] = $value;
            }

        }

        print_r($productData);

        // Create the simple product
        $attributeSetId = $data->getAttributeSetId();
        $typeId = $data->getTypeId();
        $productId = $this->_productApi->create($typeId, $attributeSetId, $data->getMagentoSku(), $productData);

        if ($typeId == 'configurable') {
            $configurableAttributeIds = array();
            if (is_array($data->getMagentoConfigurableAttribute())) {
                // multiple configurable attributes
                foreach ($data->getMagentoConfigurableAttribute() as $configurableAttributeCode) {
                    $configurableAttributeId = $helperAttribute->getAttributeIdByCode($configurableAttributeCode);
                    if (!$configurableAttributeId)
                        Mage::throwException('Configurable product set configurable attribute: Can not get id of attribute '.$configurableAttributeCode);
                    $configurableAttributeIds[] = $configurableAttributeId;
                }
            } else {
                // single configurable attribute
                $configurableAttributeId = $helperAttribute->getAttributeIdByCode($data->getMagentoConfigurableAttribute());
                if (!$configurableAttributeId) {
                    Mage::throwException('Configurable product set configurable attribute: Can not get id of attribute '.$data->getMagentoConfigurableAttribute());
                }
                $configurableAttributeIds[] = $configurableAttributeId;
            }
            $product = $this->_getProduct($productId);
            $product->getTypeInstance()->setUsedProductAttributeIds($configurableAttributeIds);
            $configurableAttributesData = $product->getTypeInstance()->getConfigurableAttributesAsArray();
            $product->setCanSaveConfigurableAttributes(true);
            $product->setConfigurableAttributesData($configurableAttributesData);
            $product->save();
        } else if ($typeId == 'simple' && $data->getMagentoParentSku()) {
            $configurableProduct = Mage::getModel('catalog/product')->loadByAttribute('sku', $data->getMagentoParentSku());

            if (!$configurableProduct->getId()) {
                Mage::log('Parent product with sku "' . $data->getMagentoParentSku() . '" does not exists', Zend_Log::ERR, 'stenik_sync.log', true);
            } elseif ($configurableProduct->getTypeId() != Mage_Catalog_Model_Product_Type_Configurable::TYPE_CODE) {
                Mage::log('Parent product with sku "' . $data->getMagentoParentSku() . '" exists, but it\'s type is not configurable', Zend_Log::ALERT, 'stenik_sync.log', true);
            } else {
                // get current childen
                $childenIds = Mage::getResourceSingleton('catalog/product_type_configurable')->getChildrenIds($configurableProduct->getId());
                $currentIds = array_values($childenIds[0]);
                // merge the new product id with current children
                $allIds = array_unique(array_merge($currentIds, array($productId)));
                // save product association
                Mage::getResourceSingleton('catalog/product_type_configurable')
                    ->saveProducts($configurableProduct, $allIds);
            }

        }

        // Update Category IDs
        $this->_updateCategories($productId, $data);

        // Update UpSell,CrossSell and Related products
        $this->_updateLinks($productId, $data);

        // Update Media Gallery
        $this->_updateGallery($productId, $data);

        $this->_clearProduct($productId);
        return $productId;
    }

    /**
     * Retrieve product's default data
     *
     * @return array
     */
    protected function _getDefaultProductData()
    {
        if (is_null($this->_defaultProductData) || !is_array($this->_defaultProductData)) {
            $this->_defaultProductData = array();
            $this->_defaultProductData['status'] = Mage_Catalog_Model_Product_Status::STATUS_ENABLED;
            $this->_defaultProductData['visibility'] = Mage_Catalog_Model_Product_Visibility::VISIBILITY_NOT_VISIBLE;
            $this->_defaultProductData['tax_class_id'] = 1;
            $websites = Mage::app()->getWebsites(true);
            $this->_defaultProductData['websites'] = array_keys($websites);
        }
        return $this->_defaultProductData;
    }

    /**
     * Update product
     *
     * @param  mixed $product
     * @param  array|Varien_Object $data
     * @return self
     */
    public function update($product, $data)
    {
        if (is_array($data)) {
            $data = new Varien_Object($data);
        } else if (!($data instanceof Varien_Object)) {
            trigger_error(sprintf('%s() expects parameter 1 to be array or Varien_Object, %s given.', __FUNCTION__, gettype($data)), E_USER_WARNING);
            return;
        }

        $productId = null;
        if (is_numeric($product)) {
            $productId = $product;
        } else if ($product instanceof Mage_Catalog_Model_Product) {
            $this->_setProduct($product);
            $productId = $product->getId();
        }

        if (!$productId) {
            return $this;
        }

        $stockData = $this->_extractStockData($data);

        $this->_prepareData($data);

        if (count($data->getData())) {
            $attributesToUpdate = $data->getData();
            /**
             * When updating product these attributes should not be set!
             */
            unset($attributesToUpdate['magento_parent_sku']);
            unset($attributesToUpdate['magento_configurable_attribute']);
            unset($attributesToUpdate['type_id']);
            unset($attributesToUpdate['attribute_set_id']);
            unset($attributesToUpdate['force_update']);
            unset($attributesToUpdate['category_ids']);
            unset($attributesToUpdate['up_sell_ids']);
            unset($attributesToUpdate['cross_sell_ids']);
            unset($attributesToUpdate['related_ids']);
            unset($attributesToUpdate['group_price']);
            unset($attributesToUpdate['store_id']);
            unset($attributesToUpdate['gallery']);

            $helperAttribute = Mage::helper('stenik_sync/attribute');

            /**
             * Handle update by value
             */
            foreach ($attributesToUpdate as $attributeCode => $value) {
                if ($helperAttribute->attributeUsesDefaultSource($attributeCode) && $helperAttribute->attributeIsSelect($attributeCode)) {
                    // If attribute uses source and its value is given by label, then check it and create a new one if necessary
                    $attributeOptionId = $helperAttribute->getOptionByLabel($attributeCode, $value);
                    if (!$attributeOptionId) {
                        $attributeOptionId = $helperAttribute->createOption($attributeCode, $value);
                    }
                    if ($attributeOptionId) {
                        $attributesToUpdate[$attributeCode] = $attributeOptionId;
                    }
                }
                if ($helperAttribute->attributeUsesDefaultSource($attributeCode) && $helperAttribute->attributeIsMultiselect($attributeCode)) {
                    // If attribute uses source and its value is given by label, then check it and create a new one if necessary
                    if (!is_array($value)) {
                        $value = array($value);
                    }
                    // unset the labels
                    $attributesToUpdate[$attributeCode] = false;
                    // get or create options ids for each value
                    foreach ($value as $singleValue) {
                        $attributeOptionId = $helperAttribute->getOptionByLabel($attributeCode, $singleValue);
                        if (!$attributeOptionId) {
                            $attributeOptionId = $helperAttribute->createOption($attributeCode, $singleValue);
                        }
                        if ($attributeOptionId) {
                            $attributesToUpdate[$attributeCode][] = $attributeOptionId;
                        }
                    }
                    // implode the options ids
                    $attributesToUpdate[$attributeCode] = implode(',', $attributesToUpdate[$attributeCode]);
                }
            }

            $storeId = $data->getStoreId() ? : Mage_Core_Model_App::ADMIN_STORE_ID;
            echo "Updating attributes with values (Item ID {$productId}, STORE {$storeId}):\n";
            print_r($attributesToUpdate);

            Mage::getSingleton('catalog/product_action')->updateAttributes(
                array($productId),
                $attributesToUpdate,
                $storeId
            );
        }

        if (count($stockData->getData())) {
            echo "Updating stock\n";
            print_r($stockData->getData());

            $stockItem = Mage::getModel('cataloginventory/stock_item')->loadByProduct($productId);
            if (!$stockItem->getId()) {
                $stockItem->setData('product_id', $productId);
                $stockItem->setData('stock_id', 1); // Use the default stock
            }
            $stockItem->setData('use_config_manage_stock', 0);
            $stockItem->setData('manage_stock', 1);

            $stockItem->addData($stockData->getData());
            if ($stockItem->getProductId() != $productId) {
                $stockItem->assignProduct($this->_getProduct($productId));
            }

            $stockItem->save();
        }

        if ($data->getGroupPrice()) {
            echo "Updating group price\n";
            print_r($data->getGroupPrice());

            $this->_productApi->update($productId, array(
                'group_price' => $data->getGroupPrice()
            ));
        }

        if ($data->getMagentoParentSku()) {
            // Try to get the configurable product by sku
            $configurableProduct = Mage::getModel('catalog/product')->loadByAttribute('sku', $data->getMagentoParentSku());

            if (!$configurableProduct->getId()) {
                Mage::log('Parent product with sku "' . $data->getMagentoParentSku() . '" does not exists', Zend_Log::ERR, 'stenik_sync.log', true);
            } elseif ($configurableProduct->getTypeId() != Mage_Catalog_Model_Product_Type_Configurable::TYPE_CODE) {
                Mage::log('Parent product with sku "' . $data->getMagentoParentSku() . '" exists, but it\'s type is not configurable', Zend_Log::ALERT, 'stenik_sync.log', true);
            } else {
                // Get configurable product current children
                $childenIds = Mage::getResourceSingleton('catalog/product_type_configurable')->getChildrenIds($configurableProduct->getId());
                $currentIds = array_values($childenIds[0]);
                // merge the new product id with current children
                $allIds = array_unique(array_merge($currentIds, array($productId)));
                // save product association
                Mage::getResourceSingleton('catalog/product_type_configurable')
                    ->saveProducts($configurableProduct, $allIds);
                $configurableProduct->clearInstance();
            }
        }

        // Update Category IDs
        $this->_updateCategories($productId, $data);

        // Update UpSell,CrossSell and Related products
        $this->_updateLinks($productId, $data);

        // Update Media Gallery
        $this->_updateGallery($productId, $data);

        $this->_clearProduct($productId);
        echo "\n\n";
        return $this;
    }

    /**
     * Set Product
     *
     * @param  Mage_Catalog_Model_Product $product
     * @return this
     */
    protected function _setProduct(Mage_Catalog_Model_Product $product)
    {
        if ($product->getId()) {
            $this->_products[$product->getId()] = $product;
        }

        return $this;
    }

    /**
     * Retrieve Product
     *
     * @param  integer $productId
     * @return Mage_Catalog_Model_Product
     */
    protected function _getProduct($productId)
    {
        if (!isset($this->_products[$productId])) {
            $this->_products[$productId] = Mage::getModel('catalog/product')->load($productId);
        }

        return $this->_products[$productId];
    }

    /**
     * Clear Product
     *
     * @param  integer $productId
     * @return this
     */
    protected function _clearProduct($productId)
    {
        if (isset($this->_products[$productId])) {
            $this->_products[$productId]->clearInstance();
            unset($this->_products[$productId]);
        }

        return $this;
    }

    /**
     * Update Category IDs
     *
     * @param  integer       $productId
     * @param  Varien_Object $data
     * @return void
     */
    protected function _updateCategories($productId, $data)
    {
        if(!$data->hasCategoryIds()) {
            return;
        }

        echo "Updating category associations:\n";
        $productData = array('categories' => $data->getCategoryIds());
        print_r($productData);

        $this->_productApi->update($productId, $productData);
    }

    /**
     * Update UpSell,CrossSell and Related products
     *
     * @param  integer       $productId
     * @param  Varien_Object $data
     * @return void
     */
    protected function _updateLinks($productId, $data)
    {
        $product = null;

        if ($data->hasUpSellIds() || $data->hasCrossSellIds() || $data->hasRelatedIds()) {
            $product = $this->_getProduct($productId);
            echo "Updating links:\n";
        }

        if ($data->hasUpSellIds()) {
            $product->setUpSellLinkData(array_fill_keys($data->getUpSellIds(), array('position' => '')));
            echo 'UpSell: '; print_r($product->getUpSellLinkData());
        }

        if ($data->hasCrossSellIds()) {
            $product->setCrossSellLinkData(array_fill_keys($data->getCrossSellIds(), array('position' => '')));
            echo 'CrossSell: '; print_r($product->getCrossSellLinkData());
        }

        if ($data->hasRelatedIds()) {
            $product->setRelatedLinkData(array_fill_keys($data->getRelatedIds(), array('position' => '')));
            echo 'Related: '; print_r($product->getRelatedLinkData());
        }

        if ($data->hasUpSellIds() || $data->hasCrossSellIds() || $data->hasRelatedIds()) {
            Mage::getSingleton('catalog/product_link')->saveProductRelations($product);
        }
    }

    /**
     * Update media gallery
     *
     * @param  integer       $productId
     * @param  Varien_Object $data
     * @return void
     */
    protected function _updateGallery($productId, $data)
    {
        if ($data->getGallery()) {
            echo "Update gallery: \n"; print_r($data->getGallery()); echo "\n";

            $product = $this->_getProduct($productId);

            $mediaGalleryBackend = Mage::getResourceModel('catalog/product')->getAttribute(Mage_Catalog_Model_Api2_Product_Image_Rest::GALLERY_ATTRIBUTE_CODE)->getBackend();
            foreach ($data->getGallery() as $galleryImage) {
                $imageFileUri = $mediaGalleryBackend->addImage($product, $galleryImage['filepath'], null, true, false);

                if (isset($galleryImage['image']) && $galleryImage['image']) {
                    $product->setData('image', $imageFileUri);
                }

                if (isset($galleryImage['small_image']) && $galleryImage['small_image']) {
                    $product->setData('small_image', $imageFileUri);
                }

                if (isset($galleryImage['thumbnail']) && $galleryImage['thumbnail']) {
                    $product->setData('thumbnail', $imageFileUri);
                }

            }

            /**
             * @todo Try partial save.
             */
            $product->save();
        }
    }

    /**
     * Prepare data
     *
     * @param  Varien_Object $data
     * @return void
     */
    protected function _prepareData(Varien_Object $data)
    {
        $data->unsetData('id');
        $data->unsetData('sku');
        $data->unsetData('magento_id');
        $data->unsetData('magento_sku');
    }

    /**
     * Extract stock data
     *
     * @param  Varien_Object $data
     * @return Varien_Object
     */
    protected function _extractStockData(Varien_Object $data)
    {
        $result = new Varien_Object();
        $keys = array('qty', 'is_in_stock', 'use_config_backorders', 'backorders');
        foreach ($keys as $key) {
            if (!is_null($data->getData($key, null))) {
                $result->setData($key, $data->getData($key));
                $data->unsetData($key);
            }
        }
        return $result;
    }
}
