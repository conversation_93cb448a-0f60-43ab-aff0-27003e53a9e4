<?php
/**
 * Status Model
 *
 * @package Stenik_Sync
 * <AUTHOR> Magento Team <<EMAIL>>
 */

/**
 * @method this setInfo(Varien_Object $info)
 */
class Stenik_Sync_Model_Status extends Mage_Core_Model_Abstract
{
    const CONFIG_PATH = 'stenik_sync/status/%s';

    const STATUS_PROCESS_STARTED           = 1;
    const STATUS_PROCESS_FINISHED_NO_ERROR = 2;
    const STATUS_PROCESS_FINISHED_ERROR    = 4;

    /**
     * Status key
     *
     * @var string
     */
    protected $_key = 'sync';

    /**
     * Factory no object
     *
     * @param  string|null $key
     * @return Stenik_Sync_Model_Status
     */
    public function factory($key = null)
    {
        $className = get_class($this);
        $model = new $className();

        if ($key) {
            $model->setKey($key);
        }

        return $model;
    }

    /**
     * Retrieve all status models
     *
     * @return array
     */
    public function getAllStatusModels()
    {
        $models = array();

        $configPath = trim(sprintf(self::CONFIG_PATH, ''), '/');
        $nodes = Mage::getConfig()->getNode($configPath, 'default', Mage_Core_Model_App::ADMIN_STORE_ID);
        if ($nodes) {
            $nodes = (array) $nodes;

            foreach (array_keys($nodes) as $key) {
                $models[] = $this->factory($key);
            }
        }

        return $models;
    }

    /**
     * Retrieve info
     *
     * @return mixed
     */
    public function getInfo()
    {
        if (!$this->hasData('info')) {
            $info = Mage::getStoreConfig($this->getConfigPath(), Mage_Core_Model_App::ADMIN_STORE_ID);
            $info = $info ? json_decode($info, true) : array();
            $info = new Varien_Object($info);
            $this->setData('info', $info);
        }

        return $this->getData('info');
    }

    /**
     * Set key
     *
     * @param mixed $info
     * @return this
     */
    public function setKey($key)
    {
        $this->_key = $key;
        return $this;
    }

    /**
     * Retrieve key
     *
     * @return mixed
     */
    public function getKey()
    {
        return $this->_key;
    }

    /**
     * Retrieve config path
     *
     * @return string
     */
    public function getConfigPath()
    {
        return sprintf(self::CONFIG_PATH, $this->_key);
    }


    /**
     * Set process status
     *
     * @param string      $processCode
     * @param integer     $status
     * @param string      $$message
     * @param string|null $date
     * @return this
     */
    public function setProcessStatus($processCode, $status, $message = '', $date = null)
    {
        return $this->addProcessData($processCode, array(
            'status'         => $status,
            'status_message' => $message,
            'status_label'   => $this->getStatusLabel($status),
            'status_date'    => $date ? : Varien_Date::now(),
        ));
    }

    /**
     * Retrieve status label
     *
     * @param  integer $status
     * @return void
     */
    public function getStatusLabel($status)
    {
        switch ($status) {
            case self::STATUS_PROCESS_STARTED: return 'Started';
            case self::STATUS_PROCESS_FINISHED_NO_ERROR: return 'Finished';
            case self::STATUS_PROCESS_FINISHED_ERROR: return 'Finished with error';
            default: return $status;
        }
    }

    /**
     * Set process data and save
     *
     * @param string $processCode
     * @param array  $data
     * @return this
     */
    public function addProcessData($processCode, $data)
    {
        $this->_addProcessData($processCode, $data);
        $this->_save();
        return $this;
    }

    /**
     * Retrieve processes info
     *
     * @return array of Varien_Object
     */
    public function getProcessesInfo()
    {
        $processes = array();
        if ($processesData = $this->getInfo()->getProcesses()) {
            foreach ($processesData as $processData) {
                $processes[] = new Varien_Object($processData);
            }
        }

        return $processes;
    }

    /**
     * Set process data
     *
     * @param string $processCode
     * @param array  $data
     * @return this
     */
    public function _addProcessData($processCode, $data)
    {
        $info = $this->getInfo();
        $data['code'] = $processCode;
        $info->setData(array_replace_recursive($info->getData(), array(
            'processes' => array(
                $processCode => $data
            )
        )));
        return $this;
    }


    /**
     * Save
     *
     * @return this
     */
    protected function _save()
    {
        Mage::getConfig()->saveConfig($this->getConfigPath(), json_encode($this->getInfo()->getData()), 'default', Mage_Core_Model_App::ADMIN_STORE_ID);
        return $this;
    }
}
