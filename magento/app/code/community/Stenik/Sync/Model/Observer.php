<?php
/**
 * Observer
 *
 * @package Stenik_Sync
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_Sync_Model_Observer extends Mage_Core_Model_Abstract
{
    /**
     * Adminhtml block prepare layout after
     *
     * @param  Varien_Event_Observer $observer
     * @return void
     */
    public function adminhtmlBlockPrepareLayoutAfter(Varien_Event_Observer $observer)
    {
        $block = $observer->getEvent()->getBlock();

        if ($block instanceof Mage_Adminhtml_Block_Catalog_Product_Edit) {
            $product = $block->getProduct();
            $url = $block->getUrl('stenik_sync/adminhtml_sync/product', array('id' => $product->getId()));

            $button = $block->getLayout()->createBlock('adminhtml/widget_button')
                ->setData(array(
                    'label'     => Mage::helper('catalog')->__('Sync Product'),
                    'onclick'   => "setLocation('$url')",
                    'class'     => 'custom-window-open-btn',
                    'id'        => 'sync_product_button',
                    'style'     => ''
                ));

            $block->getChild('back_button')->setAfterHtml(
                $block->getChild('back_button')->getAfterHtml() . $button->toHtml()
            );
        }

        if ($block instanceof Mage_Adminhtml_Block_Catalog_Product) {
            $url = $block->getUrl('stenik_sync/adminhtml_sync/allProducts');

            $block->addButton('sync_all_products_button', array(
                'label'     => Mage::helper('catalog')->__('Sync All Products'),
                'onclick'   => "setLocation('$url')",
                'class'     => 'custom-window-open-btn',
                'id'        => 'sync_all_products_button',
                'style'     => ''
            ), 0, -1, 'footer');
        }
    }
}