<?xml version="1.0"?>
<config>
    <modules>
        <Stenik_Sync>
            <version>1.2.1</version>
        </Stenik_Sync>
    </modules>
    <global>
        <models>
            <stenik_sync>
                <class>Stenik_Sync_Model</class>
                <resourceModel>stenik_sync_resource_default</resourceModel>
            </stenik_sync>

            <stenik_sync_dry>
                <class>Stenik_Sync_Model</class>
                <resourceModel>stenik_sync_resource_dry</resourceModel>
            </stenik_sync_dry>

            <stenik_sync_resource_default>
                <class>Stenik_Sync_Model_Resource_Default</class>
            </stenik_sync_resource_default>

            <stenik_sync_resource_dry>
                <class>Stenik_Sync_Model_Resource_Dry</class>
            </stenik_sync_resource_dry>
        </models>

        <helpers>
            <stenik_sync>
                <class>Stenik_Sync_Helper</class>
            </stenik_sync>
        </helpers>

        <blocks>
            <stenik_sync>
                <class>Stenik_Sync_Block</class>
            </stenik_sync>
        </blocks>


        <resources>
            <stenik_sync_setup>
                <connection>
                    <use>default_setup</use>
                </connection>
            </stenik_sync_setup>
            <stenik_sync_write>
                <connection>
                    <use>stenik_sync_setup</use>
                </connection>
            </stenik_sync_write>
            <stenik_sync_read>
                <connection>
                    <use>stenik_sync_setup</use>
                </connection>
            </stenik_sync_read>
        </resources>

    </global>

    <admin>
        <routers>
            <stenik_sync>
                <use>admin</use>
                <args>
                    <module>Stenik_Sync</module>
                    <frontName>stenik_sync</frontName>
                </args>
            </stenik_sync>
        </routers>
    </admin>

    <adminhtml>
        <layout>
            <updates>
                <stenik_sync>
                    <file>stenik_sync.xml</file>
                </stenik_sync>
            </updates>
        </layout>

        <events>
            <core_block_abstract_prepare_layout_after>
                <observers>
                    <stenik_sync>
                        <type>singleton</type>
                        <class>stenik_sync/observer</class>
                        <method>adminhtmlBlockPrepareLayoutAfter</method>
                    </stenik_sync>
                </observers>
            </core_block_abstract_prepare_layout_after>
        </events>
    </adminhtml>

    <stenik_sync>
        <inventory>
            <source>
                <class/>
            </source>
        </inventory>
        <price>
            <source>
                <class/>
            </source>
        </price>
    </stenik_sync>

</config>
