<?php
/**
 * @package  Stenik_Sync
 * <AUTHOR> Magento Team <<EMAIL>>
 */


class Stenik_Sync_Adminhtml_SyncController extends Mage_Adminhtml_Controller_Action
{
    /**
     * Sync product action
     *
     * @return void
     */
    public function productAction()
    {
        try {
            $product = Mage::getModel('catalog/product')->load($this->getRequest()->getParam('id'));

            if (!$product->getId()) {
                Mage::throwException($this->__('Invalid product.'));
            }

            $sync = Mage::helper('stenik_sync')->factoryCatalogProduct();
            ob_start();
            $sync->syncSingle($product);
            $content = ob_get_contents();
            ob_end_clean();

            $this->_getSession()->addSuccess($this->__('The product is synced successfully.'));
            $this->_getSession()->addSuccess(nl2br($content));
        } catch (Mage_Core_Exception $e) {
            $this->_getSession()->addError($e->getMessage());
        }
        $this->_redirectReferer();
    }

    /**
     * Sync all products action
     *
     * @return void
     */
    public function allProductsAction()
    {
        return;
        try {
            Mage::getModel('stenik_sync/sync_inventory')->sync();
            Mage::getModel('stenik_sync/sync_price')->sync();

            $this->_getSession()->addSuccess($this->__('All product was synced successfully.'));
        } catch (Mage_Core_Exception $e) {
            $this->_getSession()->addError($e->getMessage());
        }
        $this->_redirectReferer();
    }
}