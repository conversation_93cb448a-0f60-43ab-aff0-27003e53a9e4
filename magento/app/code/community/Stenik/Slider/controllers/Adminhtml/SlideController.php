<?php
/**
 * @package  Stenik_Slider
 * <AUTHOR> <<EMAIL>>
 */

class Stenik_Slider_Adminhtml_SlideController extends Mage_Adminhtml_Controller_Action
{
	/**
	 * init.
	 * @return void
	 */
	protected function _construct() {
		$this->setUsedModuleName('Stenik_Slider');
	}

	/**
	 * Set active menu and load layout
	 *
	 * @return Stenik_Slider_Adminhtml_SlideController
	 */
	protected function _initLayout()
	{
		$this->loadLayout()
			->_setActiveMenu('stenik_slider/slide')
			->_addBreadcrumb($this->__('Slider'), $this->__('Slider'))
			->_addBreadcrumb($this->__('Slide'), $this->__('Slide'))
			;
		return $this;
	}

	/**
	 * Init slide
	 *
	 * @return Stenik_Slider_Model_Slide
	 */
	protected function _initSlide()
	{
		if( $id = $this->getRequest()->getParam('id') ) {
			$slide = Mage::getModel('stenik_slider/slide')->load($id);
			if ($slide->getId()) {
				Mage::register('stenik_slider_slide', $slide);
				return $slide;
			}
		}
		return Mage::getModel('stenik_slider/slide');
	}

	/**
	 * Index action
	 * @return void
	 */
	public function indexAction()
	{
		$this->_title($this->__("Slide"));
		$this->_initLayout()->renderLayout();
	}

	/**
	 * grid box action
	 * @return void
	 */
	public function gridAction()
	{
		$this->getLayout()->getUpdate()->addHandle('formkey');
		$this->loadLayout();
		$this->renderLayout();
	}

	/**
	 * Create new slide action
	 * @return void
	 */
	public function newAction()
	{
		$this->_forward('edit');
	}

	/**
	 * Edit slide action
	 * @return void
	 */
	public function editAction()
	{
		$model  = $this->_initSlide();

		$data = Mage::getSingleton('adminhtml/session')->getFormData(true);
		if (!empty($data)) {
			$model->setData($data);
		}

		$this->_initLayout()->renderLayout();
	}

	/**
	 * Save slide aciton
	 * @return void
	 */
	public function saveAction()
	{
		if ($data = $this->getRequest()->getPost()) {
			$slide = $this->_initSlide();

			try {
				if (isset($_FILES['image']['name']) && $_FILES['image']['name'] != '') {

					$uploader = new Varien_File_Uploader('image');

					$uploader->setAllowedExtensions(array('jpg','jpeg','gif','png'));
					$uploader->setAllowRenameFiles(true);
					$uploader->setFilesDispersion(false);

					$media_path  = Mage::getBaseDir('media') . DS . 'slider' . DS . 'image' . DS;

					$uploadResult = $uploader->save($media_path, $_FILES['image']['name']);
					if ($uploadResult)
						$data['image'] = 'slider' . DS . 'image' . DS . $uploadResult['file'];
					else $data['image'] = '';


				} else {
					if(isset($data['image']['delete']) && $data['image']['delete'] == 1) {
						$data['image'] = '';
					} else {
						unset($data['image']);
					}
				}

				$slide->addData($data);
				$slide->save();

				$this->_getSession()->addSuccess($this->__('Slide succesfully saved.'));

			} catch (Mage_Core_Exception $e) {
				$this->_getSession()->addError($e->getMessage());
			} catch (Exception $e) {
				$this->_getSession()->addException($e, $this->__('An error occurred while saving the slide.'));
			}

			$this->_getSession()->setFormData(false);

			if ($this->getRequest()->getParam('back', false))
				return $this->_redirect('*/*/edit', array('_current' => true, 'id'=>$slide->getId()));
		}


		$this->_redirect('*/*/');
	}

	/**
	 * Delete slide aciton
	 * @return void
	 */
	public function deleteAction()
	{
		$slide = $this->_initSlide();

		if ($slide->getId()) {
			try {
				$slide->delete();
				$this->_getSession()->addSuccess($this->__('Slide succesfully deleted.'));
			} catch (Mage_Core_Exception $e) {
				$this->_getSession()->addError($e->getMessage());
			} catch (Exception $e) {
				$this->_getSession()->addException($e, $this->__('An error occurred while deleting the slide.'));
			}
		} else {
			$this->_getSession()->addSuccess($this->__('Invalid slide id.'));
		}

		$this->_redirect('*/*/');
	}

	/**
	 * Mass Delete Action
	 * @return void
	 */
	public function massDeleteAction()
	{
		$slideIds = $this->getRequest()->getParam('slide_ids');
		if (!is_array($slideIds)) {
			$this->_getSession()->addError($this->__('Please select slide(s).'));
		} else {
			if (!empty($slideIds)) {
				try {
					foreach ($slideIds as $slideId) {
						$slide = Mage::getSingleton('stenik_slider/slide')->load($slideId);
						Mage::dispatchEvent('stenik_slider_controller_slide_delete', array('slide' => $slide));
						$slide->delete();
					}
					$this->_getSession()->addSuccess(
						$this->__('Total of %d record(s) have been deleted.', count($slideIds))
					);
				} catch (Exception $e) {
					$this->_getSession()->addError($e->getMessage());
				}
			}
		}
		$this->_redirect('*/*/index');
	}

	/**
	 * Update slide(s) status action
	 * @return  void
	 */
	public function massStatusAction()
	{
		$slideIds = (array)$this->getRequest()->getParam('slide_ids');
		$status     = (int)$this->getRequest()->getParam('status');

		try {
			$updated = 0;
			foreach ($slideIds as $slideId) {
				$slide = Mage::getSingleton('stenik_slider/slide')->load($slideId);
				if ($slide->getStatus() != $status) {
					$slide->setStatus($status)->save();
					$updated++;
				}
			}
			$this->_getSession()->addSuccess(
				$this->__('Total of %d record(s) have been updated.', $updated)
			);
		}
		catch (Mage_Core_Model_Exception $e) {
			$this->_getSession()->addError($e->getMessage());
		} catch (Mage_Core_Exception $e) {
			$this->_getSession()->addError($e->getMessage());
		} catch (Exception $e) {
			$this->_getSession()
				->addException($e, $this->__('An error occurred while updating the slide(s) status.'));
		}

		$this->_redirect('*/*/index');
	}

	/**
	 * Is allowed
	 *
	 * @return boolean
	 */
	protected function _isAllowed()
	{
	    return Mage::getSingleton('admin/session')->isAllowed('stenik_slider/slides');
	}
}