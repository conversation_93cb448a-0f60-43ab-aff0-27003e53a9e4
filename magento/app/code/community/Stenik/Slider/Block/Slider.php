<?php
/**
 * @package  Stenik_Slider
 * <AUTHOR> <<EMAIL>>
 */
/**
 * @method setSliderKey($sliderKey)
 * @method setSliderId($sliderId)
 * @method getSliderId()
 */
class Stenik_Slider_Block_Slider extends Mage_Core_Block_Template
{

	static protected $_sliders = array();

	/**
	 * Render the slider
	 *
	 * @return string
	 */
	protected function _toHtml()
	{

		if (!$this->getSliderKey())
			return '';

		return parent::_toHtml();
	}

	/**
	 * Initialize block's cache
	 *
	 * @return void
	 */
	protected function _construct()
	{
		parent::_construct();
		$this->addData(array('cache_lifetime' => 172800));
		$tag = array(Stenik_Slider_Model_Slide::CACHE_TAG);
		$tags = !$this->hasData('cache_tags') ? $tag : array_merge($this->getData('cache_tags'), $tag);
		$this->setData('cache_tags', $tags);
	}

	/**
	 * Get cache key informative items
	 *
	 * @return array
	 */
	public function getCacheKeyInfo()
	{
		//Adding date in order to create unique slider keys on a daily basis
		$currentDate = Mage::getModel('core/date')->date('Y-m-d');

		return array_merge(parent::getCacheKeyInfo(), array(
			'slider_key' => $this->getSliderKey(),
			'slider_date'=> $currentDate,
			'slider_id'  => $this->getSliderId(),
		));
	}

	/**
	 * Set slider key from current category's url key
	 *
	 * @return bool
	 */
	public function setSliderKeyFromCurrentCategoryUrlKey()
	{
		$currentCategory = Mage::registry('current_category');
		if ($currentCategory instanceof Mage_Catalog_Model_Category && $currentCategory->getUrlKey()) {
			$this->setSliderKey($currentCategory->getUrlKey());
			return true;
		}

		return false;
	}

	/**
	 * Returns the slides for current slider key
	 *
	 * @return Stenik_Slider_Model_Resource_Slide_Collection
	 */
	public function getSlides()
	{
		return self::_getSlides($this->getSliderKey());
	}

	/**
	 * Returns the slides by slider key
	 *
	 * @return Stenik_Slider_Model_Resource_Slide_Collection
	 */
	static protected function _getSlides($sliderKey)
	{
		Varien_Profiler::start(__METHOD__);
		if (!isset(self::$_sliders[$sliderKey])) {
			$slides = Mage::getModel('stenik_slider/slide')->getCollection();

			$slides->addFieldToFilter('slider_key', $sliderKey);
			$slides->addStoreFilter();
			$slides->addActiveFilter();
			$slides->addOrder('position', Varien_Data_Collection_Db::SORT_ORDER_ASC);

			self::$_sliders[$sliderKey] = $slides->load();
		}
		Varien_Profiler::stop(__METHOD__);

		return self::$_sliders[$sliderKey];
	}
}
