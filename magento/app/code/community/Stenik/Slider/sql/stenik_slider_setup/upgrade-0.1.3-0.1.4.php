<?php
/**
 * Add new columns to the stenik_slider/slide table
 *
 * @package  Stenik_Slider
 * <AUTHOR> <<EMAIL>>
 */

$installer = $this;
/* @var $installer Mage_Core_Model_Resource_Setup */

$installer->getConnection()->addColumn($installer->getTable('stenik_slider/slide'), 'show_buy_button', array(
    'type'    => Varien_Db_Ddl_Table::TYPE_INTEGER,
    'comment' => 'Show buy button'
));
$installer->getConnection()->addColumn($installer->getTable('stenik_slider/slide'), 'label_style', array(
    'type'    => Varien_Db_Ddl_Table::TYPE_TEXT,
    'length'  => 255,
    'comment' => 'Label Style'
));
$installer->getConnection()->addColumn($installer->getTable('stenik_slider/slide'), 'label_text', array(
    'type'    => Varien_Db_Ddl_Table::TYPE_TEXT,
    'length'  => 255,
    'comment' => 'Label Text'
));