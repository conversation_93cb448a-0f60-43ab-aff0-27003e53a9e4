<?php
/**
 * @package  Stenik_Slider
 * <AUTHOR> <<EMAIL>>
 */

$installer = $this;
/* @var $installer Mage_Core_Model_Resource_Setup */

$table = $installer->getConnection()
	->newTable($installer->getTable('stenik_slider/slide'))

	->addColumn('slide_id', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(
		'identity'  => true,
		'unsigned'  => true,
		'nullable'  => false,
		'primary'   => true,
		), 'Slide Id')

	->addColumn('status', Varien_Db_Ddl_Table::TYPE_SMALLINT, null, array(), 'Status (Active/Inactive)')
	->addColumn('store_id', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(), 'Store Id')
	->addColumn('title', Varien_Db_Ddl_Table::TYPE_VARCHAR, 255, array(), 'Title')
	->addColumn('slider_key', Varien_Db_Ddl_Table::TYPE_VARCHAR, 255, array(), 'Slider Key')
	->addColumn('position', Varien_Db_Ddl_Table::TYPE_INTEGER, null, array(), 'Position')
	->addColumn('image', Varien_Db_Ddl_Table::TYPE_VARCHAR, 255, array(), 'Image filename')
	->addColumn('link', Varien_Db_Ddl_Table::TYPE_VARCHAR, 255, array(), 'Link')
	->addColumn('content', Varien_Db_Ddl_Table::TYPE_TEXT, '32k', array(), 'Content')
	->addColumn('active_from_date', Varien_Db_Ddl_Table::TYPE_DATE, null, array(), 'Active From Date')
	->addColumn('active_to_date', Varien_Db_Ddl_Table::TYPE_DATE, null, array(), 'Active To Date')

	->setComment('Slides Table');

$installer->getConnection()->createTable($table);
