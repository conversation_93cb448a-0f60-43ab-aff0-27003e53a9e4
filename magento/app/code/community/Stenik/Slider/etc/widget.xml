<?xml version="1.0"?>
<!--
/**
 * @package  Stenik_Slider
 * <AUTHOR> <<EMAIL>>
 */
 -->
 <widgets>
	<stenik_slider type="stenik_slider/widget_slider">
		<name>Slider</name>
		<!-- <description type="desc">Insert a slider by slider key</description> -->
		<parameters>

			<slider_key translate="label" module="stenik_slider">
				<label>Slider Key</label>
				<required>1</required>
				<visible>1</visible>
				<type>text</type>
			</slider_key>

			<template translate="label" module="stenik_slider">
				<label>Template</label>
				<required>1</required>
				<visible>1</visible>
				<type>select</type>
				<values>
					<horizontal_default translate="label" module="stenik_slider">
						<label>Slider</label>
						<value>stenik/slider/default.phtml</value>
					</horizontal_default>
				</values>
			</template>

			<additional_class translate="label note" module="stenik_slider">
				<label>Additional class</label>
				<required>0</required>
				<visible>1</visible>
				<type>text</type>
				<note>Additional CSS Class.</note>
			</additional_class>

		</parameters>
	</stenik_slider>
 </widgets>