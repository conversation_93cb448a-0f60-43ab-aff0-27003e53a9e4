<?php
/**
 * @package  Stenik_Slider
 * <AUTHOR> <<EMAIL>>
 */

class Stenik_Slider_Model_Resource_Slide_Collection extends Mage_Core_Model_Resource_Db_Collection_Abstract
{
	/**
	 * init
	 */
	protected function _construct()
	{
		$this->_init('stenik_slider/slide');
	}


	/**
	 * @param int $storeId
	 * @return  Stenik_Slider_Model_Resource_Slide_Collection
	 */
	public function addStoreFilter($storeId = null)
	{	

		if ($storeId === null)
			$storeId = Mage::app()->getStore()->getId();

		$this->addFieldToFilter('store_id', array('or'=>array(
				array('eq' => $storeId),
				array('null' => true),
				array('eq' => 0),
			))
		);

		return $this;
	}

	/**
	 * @return  Stenik_Slider_Model_Resource_Slide_Collection
	 */
	public function addActiveFilter()
	{
		$this->addFieldToFilter('status', <PERSON><PERSON><PERSON>_Slider_Model_Slide::STATUS_ENABLED);

		$todayEndOfDayDate  = Mage::app()->getLocale()->date()->setTime('23:59:59')->toString(Varien_Date::DATETIME_INTERNAL_FORMAT);
		$todayStartOfDayDate  = Mage::app()->getLocale()->date()->setTime('00:00:00')->toString(Varien_Date::DATETIME_INTERNAL_FORMAT);

		$this->addFieldToFilter('active_from_date', array('or'=>array(
			array('date' => true, 'to' => $todayEndOfDayDate),
			array('is' => new Zend_Db_Expr('null')),
			))
		);

		$this->addFieldToFilter('active_to_date', array('or'=>array(
			array('date' => true, 'from' => $todayStartOfDayDate),
			array('is' => new Zend_Db_Expr('null')),
			))
		);

		return $this;
	}
}
