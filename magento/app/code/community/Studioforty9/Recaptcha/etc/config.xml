<?xml version="1.0"?>
<!--
/**
 * Studioforty9_Recaptcha
 *
 * @category  Studioforty9
 * @package   Studioforty9_Recaptcha
 * <AUTHOR> <<EMAIL>>
 * @copyright 2015 StudioForty9 (http://www.studioforty9.com)
 * @license   https://github.com/studioforty9/recaptcha/blob/master/LICENCE BSD
 * @version   1.5.0
 * @link      https://github.com/studioforty9/recaptcha
 */
-->
<config>
    <modules>
        <Studioforty9_Recaptcha>
            <version>1.5.0</version>
        </Studioforty9_Recaptcha>
    </modules>
    <global>
        <blocks>
            <studioforty9_recaptcha>
                <class>Studioforty9_Recaptcha_Block</class>
            </studioforty9_recaptcha>
        </blocks>
        <helpers>
            <studioforty9_recaptcha>
                <class>Studioforty9_Recaptcha_Helper</class>
            </studioforty9_recaptcha>
        </helpers>
        <models>
            <studioforty9_recaptcha>
                <class>Studioforty9_Recaptcha_Model</class>
            </studioforty9_recaptcha>
        </models>
        <resources>
            <studioforty9_recaptcha_setup>
                 <setup>
                      <module>Studioforty9_Recaptcha</module>
                      <class>Mage_Core_Model_Resource_Setup</class>
                 </setup>
            </studioforty9_recaptcha_setup>
        </resources>
    </global>
    <frontend>
        <layout>
            <updates>
                <studioforty9_recaptcha>
                    <file>studioforty9_recaptcha.xml</file>
                </studioforty9_recaptcha>
            </updates>
        </layout>
        <translate>
            <modules>
                <studioforty9_recaptcha>
                    <files>
                        <default>Studioforty9_Recaptcha.csv</default>
                    </files>
                </studioforty9_recaptcha>
            </modules>
        </translate>
        <events>
            <controller_action_predispatch>
                <observers>
                    <studioforty9_recaptcha>
                        <class>studioforty9_recaptcha/observer</class>
                        <method>onPostPreDispatch</method>
                    </studioforty9_recaptcha>
                </observers>
            </controller_action_predispatch>
            <studioforty9_recaptcha_failed_review_product_post>
                <observers>
                    <studioforty9_recaptcha>
                        <class>studioforty9_recaptcha/observer</class>
                        <method>onFailedRecaptchaProductReview</method>
                    </studioforty9_recaptcha>
                </observers>
            </studioforty9_recaptcha_failed_review_product_post>
            <studioforty9_recaptcha_failed_customer_account_createpost>
                <observers>
                    <studioforty9_recaptcha>
                        <class>studioforty9_recaptcha/observer</class>
                        <method>onFailedRecaptchaCustomerRegistration</method>
                    </studioforty9_recaptcha>
                </observers>
            </studioforty9_recaptcha_failed_customer_account_createpost>
            <studioforty9_recaptcha_failed_sendfriend_product_sendmail>
                <observers>
                    <studioforty9_recaptcha>
                        <class>studioforty9_recaptcha/observer</class>
                        <method>onFailedRecaptchaSendFriend</method>
                    </studioforty9_recaptcha>
                </observers>
            </studioforty9_recaptcha_failed_sendfriend_product_sendmail>
            <studioforty9_recaptcha_failed_customer_account_loginPost>
                <observers>
                    <studioforty9_recaptcha>
                        <class>studioforty9_recaptcha/observer</class>
                        <method>onFailedRecaptchaLogin</method>
                    </studioforty9_recaptcha>
                </observers>
            </studioforty9_recaptcha_failed_customer_account_loginPost>
            <studioforty9_recaptcha_failed_customer_account_forgotpasswordpost>
                <observers>
                    <studioforty9_recaptcha>
                        <class>studioforty9_recaptcha/observer</class>
                        <method>onFailedRecaptchaForgotPassword</method>
                    </studioforty9_recaptcha>
                </observers>
            </studioforty9_recaptcha_failed_customer_account_forgotpasswordpost>
        </events>
    </frontend>
    <adminhtml>
        <acl>
            <resources>
                <admin>
                    <children>
                        <system>
                            <children>
                                <config>
                                    <children>
                                        <google>
                                            <children>
                                                <studioforty9_recaptcha translate="title" module="studioforty9_recaptcha">
                                                    <title>ReCaptcha Configuration Settings</title>
                                                </studioforty9_recaptcha>
                                            </children>
                                        </google>
                                    </children>
                                </config>
                            </children>
                        </system>
                    </children>
                </admin>
            </resources>
        </acl>
    </adminhtml>
    <default>
        <google>
            <recaptcha>
                <enabled>0</enabled>
                <enabled_contacts>0</enabled_contacts>
                <enabled_reviews>0</enabled_reviews>
                <enabled_sendfriend>0</enabled_sendfriend>
                <enabled_customer_registration>0</enabled_customer_registration>
                <site_key></site_key>
                <secret_key></secret_key>
                <theme>light</theme>
                <enabled_routes><![CDATA[]]></enabled_routes>
            </recaptcha>
        </google>
    </default>
    <phpunit>
        <suite>
            <modules>
                <Studioforty9_Recaptcha/>
            </modules>
        </suite>
    </phpunit>
</config>
