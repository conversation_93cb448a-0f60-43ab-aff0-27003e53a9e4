<?xml version="1.0"?>
<!--
/**
* Studioforty9_Recaptcha
*
* @category  Studioforty9
* @package   Studioforty9_Recaptcha
* <AUTHOR> <<EMAIL>>
* @copyright 2015 StudioForty9 (http://www.studioforty9.com)
* @license   https://github.com/studioforty9/recaptcha/blob/master/LICENCE BSD
* @version   1.5.0
* @link      https://github.com/studioforty9/recaptcha
*/
-->
<config>
    <sections>
        <google>
            <groups>
                <recaptcha translate="label title" module="studioforty9_recaptcha">
                    <label>Google reCAPTCHA</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>1000</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <enabled translate="label">
                            <label>Enabled</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </enabled>
                        <site_key translate="label">
                            <label>Site Key</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment><![CDATA[<a href="https://www.google.com/recaptcha/admin" target="_blank">Create a site key</a>]]></comment>
                            <depends><enabled>1</enabled></depends>
                        </site_key>
                        <secret_key translate="label">
                            <label>Secret Key</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment><![CDATA[<a href="https://www.google.com/recaptcha/admin" target="_blank">Create a secret key</a>]]></comment>
                            <depends><enabled>1</enabled></depends>
                        </secret_key>
                        <theme translate="label">
                            <label>Theme</label>
                            <frontend_type>select</frontend_type>
                            <source_model>studioforty9_recaptcha/source_theme</source_model>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment><![CDATA[See the Google reCAPTCHA Documentation <a href="https://developers.google.com/recaptcha/docs/display#render_param" target="_blank">for more</a>]]></comment>
                            <depends><enabled>1</enabled></depends>
                        </theme>
                        <type translate="label">
                            <label>Type</label>
                            <frontend_type>select</frontend_type>
                            <source_model>studioforty9_recaptcha/source_type</source_model>
                            <sort_order>41</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment><![CDATA[See the Google reCAPTCHA Documentation <a href="https://developers.google.com/recaptcha/docs/display#render_param" target="_blank">for more</a>]]></comment>
                            <depends><enabled>1</enabled></depends>
                        </type>
                        <size translate="label">
                            <label>Size</label>
                            <frontend_type>select</frontend_type>
                            <source_model>studioforty9_recaptcha/source_size</source_model>
                            <sort_order>42</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment><![CDATA[See the Google reCAPTCHA Documentation <a href="https://developers.google.com/recaptcha/docs/display#render_param" target="_blank">for more</a>]]></comment>
                            <depends><enabled>1</enabled></depends>
                        </size>
                        <enabled_routes translate="label hint">
                            <label>Enabled Routes</label>
                            <frontend_type>multiselect</frontend_type>
                            <source_model>studioforty9_recaptcha/source_routes</source_model>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <comment><![CDATA[Hold Ctrl/Command and click to select<br/>Hold Shift and click to select a range<br/>Routes to allow reCAPTCHA verification - <a href="https://recaptcha.readme.io/v1.5/docs/troubleshooting#understanding-configuration-routes" target="_blank">read docs</a>]]></comment>
                            <depends><enabled>1</enabled></depends>
                        </enabled_routes>
                    </fields>
                </recaptcha>
            </groups>
        </google>
    </sections>
</config>