<?xml version="1.0"?>
<!--
/**
 * Vip-watches Analytics Admin Configuration
 * 
 * This file defines the admin menu structure and access control list (ACL)
 * for the Analytics module. It creates menu items in the admin panel
 * and sets permissions for different admin users.
 * 
 * @category   Vipwatches
 * @package    Vipwatches_Analytics
 * <AUTHOR> Development Team
 */
-->
<config>
    <!-- Admin menu configuration -->
    <menu>
        <!-- Create a new top-level menu item -->
        <vipwatches_analytics translate="title" module="vipwatches_analytics">
            <title>Vip-watches Analytics</title>
            <sort_order>80</sort_order>
            <children>
                <!-- Dashboard submenu item -->
                <dashboard translate="title" module="vipwatches_analytics">
                    <title>Analytics Dashboard</title>
                    <sort_order>10</sort_order>
                    <action>adminhtml/vipwatches_analytics/index</action>
                </dashboard>
                
                <!-- Store Information submenu item -->
                <store_info translate="title" module="vipwatches_analytics">
                    <title>Store Information</title>
                    <sort_order>20</sort_order>
                    <action>adminhtml/vipwatches_analytics/storeinfo</action>
                </store_info>
                
                <!-- Order Analytics submenu item -->
                <order_analytics translate="title" module="vipwatches_analytics">
                    <title>Order Analytics</title>
                    <sort_order>30</sort_order>
                    <action>adminhtml/vipwatches_analytics/orders</action>
                </order_analytics>
                
                <!-- Sales Reports submenu item -->
                <sales_reports translate="title" module="vipwatches_analytics">
                    <title>Sales Reports</title>
                    <sort_order>40</sort_order>
                    <action>adminhtml/vipwatches_analytics/sales</action>
                </sales_reports>
            </children>
        </vipwatches_analytics>
    </menu>

    <!-- Access Control List (ACL) configuration -->
    <acl>
        <resources>
            <all>
                <title>Allow Everything</title>
            </all>
            <admin>
                <children>
                    <!-- Define ACL resources for the Analytics module -->
                    <vipwatches_analytics translate="title" module="vipwatches_analytics">
                        <title>Vip-watches Analytics</title>
                        <sort_order>80</sort_order>
                        <children>
                            <!-- Dashboard access permission -->
                            <dashboard translate="title" module="vipwatches_analytics">
                                <title>Analytics Dashboard</title>
                                <sort_order>10</sort_order>
                            </dashboard>
                            
                            <!-- Store Information access permission -->
                            <store_info translate="title" module="vipwatches_analytics">
                                <title>Store Information</title>
                                <sort_order>20</sort_order>
                            </store_info>
                            
                            <!-- Order Analytics access permission -->
                            <order_analytics translate="title" module="vipwatches_analytics">
                                <title>Order Analytics</title>
                                <sort_order>30</sort_order>
                            </order_analytics>
                            
                            <!-- Sales Reports access permission -->
                            <sales_reports translate="title" module="vipwatches_analytics">
                                <title>Sales Reports</title>
                                <sort_order>40</sort_order>
                            </sales_reports>
                        </children>
                    </vipwatches_analytics>
                </children>
            </admin>
        </resources>
    </acl>
</config>
