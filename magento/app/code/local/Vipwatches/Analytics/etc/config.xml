<?xml version="1.0"?>
<!--
/**
 * Vip-watches Analytics Module Configuration
 * 
 * This is the main configuration file for the Analytics module.
 * It defines the module structure, admin routes, blocks, models, and helpers.
 * 
 * @category   Vipwatches
 * @package    Vipwatches_Analytics
 * <AUTHOR> Development Team
 */
-->
<config>
    <!-- Module version and basic information -->
    <modules>
        <Vipwatches_Analytics>
            <version>1.0.0</version>
        </Vipwatches_Analytics>
    </modules>

    <!-- Global configuration - applies to all areas (frontend, admin, etc.) -->
    <global>
        <!-- Define blocks for the module -->
        <blocks>
            <vipwatches_analytics>
                <class>Vipwatches_Analytics_Block</class>
            </vipwatches_analytics>
        </blocks>

        <!-- Define models for data handling -->
        <models>
            <vipwatches_analytics>
                <class>Vipwatches_Analytics_Model</class>
            </vipwatches_analytics>
        </models>

        <!-- Define helpers for utility functions -->
        <helpers>
            <vipwatches_analytics>
                <class>Vipwatches_Analytics_Helper</class>
            </vipwatches_analytics>
        </helpers>

        <!-- Database resource configuration -->
        <resources>
            <vipwatches_analytics_setup>
                <setup>
                    <module>Vipwatches_Analytics</module>
                </setup>
                <connection>
                    <use>core_setup</use>
                </connection>
            </vipwatches_analytics_setup>
            <vipwatches_analytics_write>
                <connection>
                    <use>core_write</use>
                </connection>
            </vipwatches_analytics_write>
            <vipwatches_analytics_read>
                <connection>
                    <use>core_read</use>
                </connection>
            </vipwatches_analytics_read>
        </resources>
    </global>

    <!-- Admin area configuration -->
    <admin>
        <!-- Define admin routes for the module -->
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <!-- Route configuration for admin controllers -->
                        <vipwatches_analytics before="Mage_Adminhtml">Vipwatches_Analytics_Adminhtml</vipwatches_analytics>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>

    <!-- Admin HTML configuration -->
    <adminhtml>
        <!-- Layout updates for admin interface -->
        <layout>
            <updates>
                <vipwatches_analytics>
                    <file>vipwatches_analytics.xml</file>
                </vipwatches_analytics>
            </updates>
        </layout>

        <!-- Translation files for admin interface -->
        <translate>
            <modules>
                <Vipwatches_Analytics>
                    <files>
                        <default>Vipwatches_Analytics.csv</default>
                    </files>
                </Vipwatches_Analytics>
            </modules>
        </translate>

        <!-- Admin menu configuration -->
        <menu>
            <!-- Create a new top-level menu item -->
            <vipwatches_analytics translate="title" module="vipwatches_analytics">
                <title>Vip-watches Analytics</title>
                <sort_order>80</sort_order>
                <children>
                    <!-- Dashboard submenu item -->
                    <dashboard translate="title" module="vipwatches_analytics">
                        <title>Analytics Dashboard</title>
                        <sort_order>10</sort_order>
                        <action>adminhtml/vipwatches_analytics/index</action>
                    </dashboard>

                    <!-- Store Information submenu item -->
                    <store_info translate="title" module="vipwatches_analytics">
                        <title>Store Information</title>
                        <sort_order>20</sort_order>
                        <action>adminhtml/vipwatches_analytics/storeinfo</action>
                    </store_info>

                    <!-- Order Analytics submenu item -->
                    <order_analytics translate="title" module="vipwatches_analytics">
                        <title>Order Analytics</title>
                        <sort_order>30</sort_order>
                        <action>adminhtml/vipwatches_analytics/orders</action>
                    </order_analytics>

                    <!-- Sales Reports submenu item -->
                    <sales_reports translate="title" module="vipwatches_analytics">
                        <title>Sales Reports</title>
                        <sort_order>40</sort_order>
                        <action>adminhtml/vipwatches_analytics/sales</action>
                    </sales_reports>
                </children>
            </vipwatches_analytics>
        </menu>

        <!-- Access Control List (ACL) configuration -->
        <acl>
            <resources>
                <all>
                    <title>Allow Everything</title>
                </all>
                <admin>
                    <children>
                        <!-- Define ACL resources for the Analytics module -->
                        <vipwatches_analytics translate="title" module="vipwatches_analytics">
                            <title>Vip-watches Analytics</title>
                            <sort_order>80</sort_order>
                            <children>
                                <!-- Dashboard access permission -->
                                <dashboard translate="title" module="vipwatches_analytics">
                                    <title>Analytics Dashboard</title>
                                    <sort_order>10</sort_order>
                                </dashboard>

                                <!-- Store Information access permission -->
                                <store_info translate="title" module="vipwatches_analytics">
                                    <title>Store Information</title>
                                    <sort_order>20</sort_order>
                                </store_info>

                                <!-- Order Analytics access permission -->
                                <order_analytics translate="title" module="vipwatches_analytics">
                                    <title>Order Analytics</title>
                                    <sort_order>30</sort_order>
                                </order_analytics>

                                <!-- Sales Reports access permission -->
                                <sales_reports translate="title" module="vipwatches_analytics">
                                    <title>Sales Reports</title>
                                    <sort_order>40</sort_order>
                                </sales_reports>
                            </children>
                        </vipwatches_analytics>
                    </children>
                </admin>
            </resources>
        </acl>
    </adminhtml>
</config>
