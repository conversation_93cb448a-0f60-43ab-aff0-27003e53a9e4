<?xml version="1.0"?>
<!--
/**
 * Vip-watches Analytics Module Configuration
 * 
 * This is the main configuration file for the Analytics module.
 * It defines the module structure, admin routes, blocks, models, and helpers.
 * 
 * @category   Vipwatches
 * @package    Vipwatches_Analytics
 * <AUTHOR> Development Team
 */
-->
<config>
    <!-- Module version and basic information -->
    <modules>
        <Vipwatches_Analytics>
            <version>1.0.0</version>
        </Vipwatches_Analytics>
    </modules>

    <!-- Global configuration - applies to all areas (frontend, admin, etc.) -->
    <global>
        <!-- Define blocks for the module -->
        <blocks>
            <vipwatches_analytics>
                <class>Vipwatches_Analytics_Block</class>
            </vipwatches_analytics>
        </blocks>

        <!-- Define models for data handling -->
        <models>
            <vipwatches_analytics>
                <class>Vipwatches_Analytics_Model</class>
            </vipwatches_analytics>
        </models>

        <!-- Define helpers for utility functions -->
        <helpers>
            <vipwatches_analytics>
                <class>Vipwatches_Analytics_Helper</class>
            </vipwatches_analytics>
        </helpers>

        <!-- Database resource configuration -->
        <resources>
            <vipwatches_analytics_setup>
                <setup>
                    <module>Vipwatches_Analytics</module>
                </setup>
                <connection>
                    <use>core_setup</use>
                </connection>
            </vipwatches_analytics_setup>
            <vipwatches_analytics_write>
                <connection>
                    <use>core_write</use>
                </connection>
            </vipwatches_analytics_write>
            <vipwatches_analytics_read>
                <connection>
                    <use>core_read</use>
                </connection>
            </vipwatches_analytics_read>
        </resources>
    </global>

    <!-- Admin area configuration -->
    <admin>
        <!-- Define admin routes for the module -->
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <!-- Route configuration for admin controllers -->
                        <vipwatches_analytics before="Mage_Adminhtml">Vipwatches_Analytics_Adminhtml</vipwatches_analytics>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>

    <!-- Admin HTML configuration -->
    <adminhtml>
        <!-- Layout updates for admin interface -->
        <layout>
            <updates>
                <vipwatches_analytics>
                    <file>vipwatches_analytics.xml</file>
                </vipwatches_analytics>
            </updates>
        </layout>

        <!-- Translation files for admin interface -->
        <translate>
            <modules>
                <Vipwatches_Analytics>
                    <files>
                        <default>Vipwatches_Analytics.csv</default>
                    </files>
                </Vipwatches_Analytics>
            </modules>
        </translate>
    </adminhtml>
</config>
