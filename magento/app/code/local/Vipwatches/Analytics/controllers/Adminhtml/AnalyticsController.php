<?php
/**
 * Vip-watches Analytics Admin Controller
 * 
 * This controller handles all admin requests for the Analytics module.
 * It provides different views for analytics data including dashboard,
 * store information, order analytics, and sales reports.
 * 
 * @category   Vipwatches
 * @package    Vipwatches_Analytics
 * <AUTHOR> Development Team
 */

class Vipwatches_Analytics_Adminhtml_AnalyticsController extends Mage_Adminhtml_Controller_Action
{
    /**
     * Initialize the controller and set up common elements
     * This method is called before any action method
     */
    protected function _initAction()
    {
        // Load the admin layout
        $this->loadLayout()
            // Set the active menu item to highlight the current section
            ->_setActiveMenu('system/vipwatches_analytics')
            // Add breadcrumbs for navigation
            ->_addBreadcrumb(
                Mage::helper('vipwatches_analytics')->__('Vip-watches'), 
                Mage::helper('vipwatches_analytics')->__('Vip-watches')
            )
            ->_addBreadcrumb(
                Mage::helper('vipwatches_analytics')->__('Analytics'), 
                Mage::helper('vipwatches_analytics')->__('Analytics')
            );
        
        return $this;
    }

    /**
     * Main dashboard action - shows overview of analytics data
     * This is the default page when accessing the Analytics module
     */
    public function indexAction()
    {
        // Set the page title
        $this->_title($this->__('Vip-watches'))->_title($this->__('Analytics Dashboard'));
        
        // Initialize the layout and add the dashboard content block
        $this->_initAction()
            ->_addContent($this->getLayout()->createBlock('vipwatches_analytics/adminhtml_dashboard'))
            ->renderLayout();
    }

    /**
     * Store information action - displays general store statistics
     * Shows information like total products, customers, categories, etc.
     */
    public function storeinfoAction()
    {
        // Set the page title
        $this->_title($this->__('Vip-watches'))->_title($this->__('Store Information'));
        
        // Set the active menu for this specific section
        $this->loadLayout()
            ->_setActiveMenu('vipwatches_analytics/store_info')
            ->_addBreadcrumb(
                Mage::helper('vipwatches_analytics')->__('Vip-watches'), 
                Mage::helper('vipwatches_analytics')->__('Vip-watches')
            )
            ->_addBreadcrumb(
                Mage::helper('vipwatches_analytics')->__('Store Information'), 
                Mage::helper('vipwatches_analytics')->__('Store Information')
            )
            ->_addContent($this->getLayout()->createBlock('vipwatches_analytics/adminhtml_storeinfo'))
            ->renderLayout();
    }

    /**
     * Order analytics action - displays detailed order statistics
     * Shows order trends, status distribution, payment methods, etc.
     */
    public function ordersAction()
    {
        // Set the page title
        $this->_title($this->__('Vip-watches'))->_title($this->__('Order Analytics'));
        
        // Set the active menu for this specific section
        $this->loadLayout()
            ->_setActiveMenu('vipwatches_analytics/order_analytics')
            ->_addBreadcrumb(
                Mage::helper('vipwatches_analytics')->__('Vip-watches'), 
                Mage::helper('vipwatches_analytics')->__('Vip-watches')
            )
            ->_addBreadcrumb(
                Mage::helper('vipwatches_analytics')->__('Order Analytics'), 
                Mage::helper('vipwatches_analytics')->__('Order Analytics')
            )
            ->_addContent($this->getLayout()->createBlock('vipwatches_analytics/adminhtml_orders'))
            ->renderLayout();
    }

    /**
     * Sales reports action - displays sales performance data
     * Shows revenue trends, top products, customer segments, etc.
     */
    public function salesAction()
    {
        // Set the page title
        $this->_title($this->__('Vip-watches'))->_title($this->__('Sales Reports'));
        
        // Set the active menu for this specific section
        $this->loadLayout()
            ->_setActiveMenu('vipwatches_analytics/sales_reports')
            ->_addBreadcrumb(
                Mage::helper('vipwatches_analytics')->__('Vip-watches'), 
                Mage::helper('vipwatches_analytics')->__('Vip-watches')
            )
            ->_addBreadcrumb(
                Mage::helper('vipwatches_analytics')->__('Sales Reports'), 
                Mage::helper('vipwatches_analytics')->__('Sales Reports')
            )
            ->_addContent($this->getLayout()->createBlock('vipwatches_analytics/adminhtml_sales'))
            ->renderLayout();
    }

    /**
     * Check if the current admin user has permission to access this controller
     * This method is called automatically by Magento before any action
     * 
     * @return bool True if access is allowed, false otherwise
     */
    protected function _isAllowed()
    {
        // Check if the user has permission to access the Analytics module
        return Mage::getSingleton('admin/session')->isAllowed('vipwatches_analytics');
    }
}
