<?php
/**
 * Vip-watches Analytics Helper
 * 
 * This helper class provides utility functions for the Analytics module.
 * It includes methods for data formatting, calculations, and common
 * operations used throughout the module.
 * 
 * @category   Vipwatches
 * @package    Vipwatches_Analytics
 * <AUTHOR> Development Team
 */

class Vipwatches_Analytics_Helper_Data extends Mage_Core_Helper_Abstract
{
    /**
     * Format currency values for display
     * 
     * @param float $amount The amount to format
     * @param string $currencyCode Optional currency code (defaults to base currency)
     * @return string Formatted currency string
     */
    public function formatCurrency($amount, $currencyCode = null)
    {
        if ($currencyCode === null) {
            // Use the base currency if no specific currency is provided
            $currencyCode = Mage::app()->getStore()->getBaseCurrencyCode();
        }
        
        // Use Magento's built-in currency formatting
        return Mage::helper('core')->currency($amount, true, false);
    }

    /**
     * Format numbers with appropriate thousand separators
     * 
     * @param int|float $number The number to format
     * @param int $decimals Number of decimal places (default: 0)
     * @return string Formatted number string
     */
    public function formatNumber($number, $decimals = 0)
    {
        return number_format($number, $decimals, '.', ',');
    }

    /**
     * Calculate percentage change between two values
     * 
     * @param float $oldValue The original value
     * @param float $newValue The new value
     * @return float Percentage change (positive for increase, negative for decrease)
     */
    public function calculatePercentageChange($oldValue, $newValue)
    {
        // Avoid division by zero
        if ($oldValue == 0) {
            return $newValue > 0 ? 100 : 0;
        }
        
        return (($newValue - $oldValue) / $oldValue) * 100;
    }

    /**
     * Format percentage for display
     * 
     * @param float $percentage The percentage value
     * @param int $decimals Number of decimal places (default: 1)
     * @return string Formatted percentage string with % symbol
     */
    public function formatPercentage($percentage, $decimals = 1)
    {
        return number_format($percentage, $decimals) . '%';
    }

    /**
     * Get date range for analytics (last 30 days by default)
     * 
     * @param int $days Number of days to go back (default: 30)
     * @return array Array with 'from' and 'to' date strings
     */
    public function getDateRange($days = 30)
    {
        $to = new DateTime();
        $from = new DateTime();
        $from->sub(new DateInterval('P' . $days . 'D'));
        
        return array(
            'from' => $from->format('Y-m-d 00:00:00'),
            'to' => $to->format('Y-m-d 23:59:59')
        );
    }

    /**
     * Format date for display in analytics
     * 
     * @param string $date Date string
     * @param string $format Display format (default: 'M j, Y')
     * @return string Formatted date string
     */
    public function formatDate($date, $format = 'M j, Y')
    {
        return Mage::helper('core')->formatDate($date, 'medium');
    }

    /**
     * Get color class for trend indicators
     * 
     * @param float $value The value to evaluate (usually a percentage)
     * @return string CSS class name for styling
     */
    public function getTrendColorClass($value)
    {
        if ($value > 0) {
            return 'trend-positive'; // Green for positive trends
        } elseif ($value < 0) {
            return 'trend-negative'; // Red for negative trends
        } else {
            return 'trend-neutral';  // Gray for neutral trends
        }
    }

    /**
     * Get trend arrow icon based on value
     * 
     * @param float $value The value to evaluate
     * @return string HTML for trend arrow
     */
    public function getTrendArrow($value)
    {
        if ($value > 0) {
            return '<span class="trend-arrow trend-up">↗</span>';
        } elseif ($value < 0) {
            return '<span class="trend-arrow trend-down">↘</span>';
        } else {
            return '<span class="trend-arrow trend-flat">→</span>';
        }
    }

    /**
     * Check if the module is enabled
     * 
     * @return bool True if module is enabled
     */
    public function isModuleEnabled()
    {
        return Mage::getStoreConfigFlag('vipwatches_analytics/general/enabled');
    }

    /**
     * Get module version
     * 
     * @return string Module version
     */
    public function getModuleVersion()
    {
        return (string) Mage::getConfig()->getNode('modules/Vipwatches_Analytics/version');
    }
}
