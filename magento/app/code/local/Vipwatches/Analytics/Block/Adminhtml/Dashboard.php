<?php
/**
 * Vip-watches Analytics Dashboard Block
 * 
 * This block handles the main dashboard display for the Analytics module.
 * It provides an overview of key metrics and statistics for the store.
 * 
 * @category   Vipwatches
 * @package    Vipwatches_Analytics
 * <AUTHOR> Development Team
 */

class Vipwatches_Analytics_Block_Adminhtml_Dashboard extends Mage_Adminhtml_Block_Widget_Container
{
    /**
     * Constructor - set up the dashboard container
     */
    public function __construct()
    {
        // Call parent constructor
        parent::__construct();
        
        // Set the block template for the dashboard
        $this->setTemplate('vipwatches/analytics/dashboard.phtml');
        
        // Set the header text for the page
        $this->_headerText = Mage::helper('vipwatches_analytics')->__('Analytics Dashboard');
    }

    /**
     * Get total number of orders in the system
     * 
     * @return int Total order count
     */
    public function getTotalOrders()
    {
        // Create a collection of all orders
        $orderCollection = Mage::getModel('sales/order')->getCollection();
        
        // Return the total count
        return $orderCollection->getSize();
    }

    /**
     * Get total revenue from all completed orders
     * 
     * @return float Total revenue amount
     */
    public function getTotalRevenue()
    {
        // Create a collection of completed orders only
        $orderCollection = Mage::getModel('sales/order')->getCollection()
            ->addFieldToFilter('status', array('in' => array('complete', 'processing')));
        
        $totalRevenue = 0;
        
        // Sum up the grand total of all completed orders
        foreach ($orderCollection as $order) {
            $totalRevenue += $order->getGrandTotal();
        }
        
        return $totalRevenue;
    }

    /**
     * Get total number of customers
     * 
     * @return int Total customer count
     */
    public function getTotalCustomers()
    {
        // Create a collection of all customers
        $customerCollection = Mage::getModel('customer/customer')->getCollection();
        
        // Return the total count
        return $customerCollection->getSize();
    }

    /**
     * Get total number of products in the catalog
     * 
     * @return int Total product count
     */
    public function getTotalProducts()
    {
        // Create a collection of all products
        $productCollection = Mage::getModel('catalog/product')->getCollection()
            ->addAttributeToFilter('status', Mage_Catalog_Model_Product_Status::STATUS_ENABLED);
        
        // Return the total count of enabled products
        return $productCollection->getSize();
    }

    /**
     * Get orders from the last 30 days
     * 
     * @return int Recent order count
     */
    public function getRecentOrders()
    {
        // Get date range for last 30 days
        $dateRange = Mage::helper('vipwatches_analytics')->getDateRange(30);
        
        // Create a collection of orders from the last 30 days
        $orderCollection = Mage::getModel('sales/order')->getCollection()
            ->addFieldToFilter('created_at', array(
                'from' => $dateRange['from'],
                'to' => $dateRange['to']
            ));
        
        return $orderCollection->getSize();
    }

    /**
     * Get revenue from the last 30 days
     * 
     * @return float Recent revenue amount
     */
    public function getRecentRevenue()
    {
        // Get date range for last 30 days
        $dateRange = Mage::helper('vipwatches_analytics')->getDateRange(30);
        
        // Create a collection of completed orders from the last 30 days
        $orderCollection = Mage::getModel('sales/order')->getCollection()
            ->addFieldToFilter('created_at', array(
                'from' => $dateRange['from'],
                'to' => $dateRange['to']
            ))
            ->addFieldToFilter('status', array('in' => array('complete', 'processing')));
        
        $recentRevenue = 0;
        
        // Sum up the grand total of recent completed orders
        foreach ($orderCollection as $order) {
            $recentRevenue += $order->getGrandTotal();
        }
        
        return $recentRevenue;
    }

    /**
     * Get average order value
     * 
     * @return float Average order value
     */
    public function getAverageOrderValue()
    {
        $totalRevenue = $this->getTotalRevenue();
        $totalOrders = $this->getTotalOrders();
        
        // Avoid division by zero
        if ($totalOrders == 0) {
            return 0;
        }
        
        return $totalRevenue / $totalOrders;
    }

    /**
     * Get top 5 selling products
     * 
     * @return array Array of product data
     */
    public function getTopProducts()
    {
        // This is a simplified version - in a real implementation,
        // you would query the sales_flat_order_item table
        $productCollection = Mage::getModel('catalog/product')->getCollection()
            ->addAttributeToSelect('name')
            ->addAttributeToFilter('status', Mage_Catalog_Model_Product_Status::STATUS_ENABLED)
            ->setPageSize(5);
        
        $topProducts = array();
        foreach ($productCollection as $product) {
            $topProducts[] = array(
                'name' => $product->getName(),
                'sku' => $product->getSku(),
                'sales' => rand(10, 100) // Placeholder - replace with actual sales data
            );
        }
        
        return $topProducts;
    }

    /**
     * Format currency for display
     * 
     * @param float $amount Amount to format
     * @return string Formatted currency
     */
    public function formatCurrency($amount)
    {
        return Mage::helper('vipwatches_analytics')->formatCurrency($amount);
    }

    /**
     * Format number for display
     * 
     * @param int|float $number Number to format
     * @return string Formatted number
     */
    public function formatNumber($number)
    {
        return Mage::helper('vipwatches_analytics')->formatNumber($number);
    }
}
