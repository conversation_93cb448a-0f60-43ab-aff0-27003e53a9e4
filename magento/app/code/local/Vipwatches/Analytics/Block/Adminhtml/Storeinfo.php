<?php
/**
 * Vip-watches Analytics Store Information Block
 * 
 * This block displays detailed information about the store including
 * configuration, statistics, and system information.
 * 
 * @category   Vipwatches
 * @package    Vipwatches_Analytics
 * <AUTHOR> Development Team
 */

class Vipwatches_Analytics_Block_Adminhtml_Storeinfo extends Mage_Adminhtml_Block_Widget_Container
{
    /**
     * Constructor - set up the store info container
     */
    public function __construct()
    {
        // Call parent constructor
        parent::__construct();
        
        // Set the block template for store information
        $this->setTemplate('vipwatches/analytics/storeinfo.phtml');
        
        // Set the header text for the page
        $this->_headerText = Mage::helper('vipwatches_analytics')->__('Store Information');
    }

    /**
     * Get basic store configuration information
     * 
     * @return array Store configuration data
     */
    public function getStoreConfig()
    {
        $store = Mage::app()->getStore();
        
        return array(
            'name' => $store->getName(),
            'website' => $store->getWebsite()->getName(),
            'base_url' => $store->getBaseUrl(),
            'secure_base_url' => $store->getBaseUrl('web', true),
            'base_currency' => $store->getBaseCurrencyCode(),
            'default_currency' => $store->getDefaultCurrencyCode(),
            'timezone' => Mage::getStoreConfig('general/locale/timezone'),
            'locale' => Mage::getStoreConfig('general/locale/code')
        );
    }

    /**
     * Get catalog statistics
     * 
     * @return array Catalog statistics
     */
    public function getCatalogStats()
    {
        // Get total products count
        $productCollection = Mage::getModel('catalog/product')->getCollection()
            ->addAttributeToFilter('status', Mage_Catalog_Model_Product_Status::STATUS_ENABLED);
        $totalProducts = $productCollection->getSize();

        // Get products by type
        $simpleProducts = Mage::getModel('catalog/product')->getCollection()
            ->addAttributeToFilter('type_id', 'simple')
            ->addAttributeToFilter('status', Mage_Catalog_Model_Product_Status::STATUS_ENABLED)
            ->getSize();

        $configurableProducts = Mage::getModel('catalog/product')->getCollection()
            ->addAttributeToFilter('type_id', 'configurable')
            ->addAttributeToFilter('status', Mage_Catalog_Model_Product_Status::STATUS_ENABLED)
            ->getSize();

        // Get categories count
        $categoryCollection = Mage::getModel('catalog/category')->getCollection()
            ->addAttributeToFilter('is_active', 1);
        $totalCategories = $categoryCollection->getSize();

        // Get out of stock products
        $outOfStockProducts = Mage::getModel('catalog/product')->getCollection()
            ->addAttributeToFilter('status', Mage_Catalog_Model_Product_Status::STATUS_ENABLED)
            ->joinField('qty', 'cataloginventory/stock_item', 'qty', 'product_id=entity_id', '{{table}}.stock_id=1', 'left')
            ->addFieldToFilter('qty', array('lteq' => 0))
            ->getSize();

        return array(
            'total_products' => $totalProducts,
            'simple_products' => $simpleProducts,
            'configurable_products' => $configurableProducts,
            'total_categories' => $totalCategories,
            'out_of_stock_products' => $outOfStockProducts,
            'in_stock_products' => $totalProducts - $outOfStockProducts
        );
    }

    /**
     * Get customer statistics
     * 
     * @return array Customer statistics
     */
    public function getCustomerStats()
    {
        // Get total customers
        $customerCollection = Mage::getModel('customer/customer')->getCollection();
        $totalCustomers = $customerCollection->getSize();

        // Get customers registered in last 30 days
        $dateRange = Mage::helper('vipwatches_analytics')->getDateRange(30);
        $recentCustomers = Mage::getModel('customer/customer')->getCollection()
            ->addFieldToFilter('created_at', array(
                'from' => $dateRange['from'],
                'to' => $dateRange['to']
            ))
            ->getSize();

        // Get newsletter subscribers
        $subscriberCollection = Mage::getModel('newsletter/subscriber')->getCollection()
            ->addFieldToFilter('subscriber_status', Mage_Newsletter_Model_Subscriber::STATUS_SUBSCRIBED);
        $newsletterSubscribers = $subscriberCollection->getSize();

        return array(
            'total_customers' => $totalCustomers,
            'recent_customers' => $recentCustomers,
            'newsletter_subscribers' => $newsletterSubscribers
        );
    }

    /**
     * Get system information
     * 
     * @return array System information
     */
    public function getSystemInfo()
    {
        return array(
            'magento_version' => Mage::getVersion(),
            'php_version' => phpversion(),
            'mysql_version' => $this->getMysqlVersion(),
            'server_software' => isset($_SERVER['SERVER_SOFTWARE']) ? $_SERVER['SERVER_SOFTWARE'] : 'Unknown',
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize')
        );
    }

    /**
     * Get MySQL version
     * 
     * @return string MySQL version
     */
    protected function getMysqlVersion()
    {
        try {
            $connection = Mage::getSingleton('core/resource')->getConnection('core_read');
            $result = $connection->query('SELECT VERSION() as version');
            $row = $result->fetch();
            return $row['version'];
        } catch (Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Get payment methods information
     * 
     * @return array Payment methods data
     */
    public function getPaymentMethods()
    {
        $paymentMethods = array();
        
        // Get all available payment methods
        $methods = Mage::getSingleton('payment/config')->getActiveMethods();
        
        foreach ($methods as $paymentCode => $paymentModel) {
            $paymentTitle = Mage::getStoreConfig('payment/' . $paymentCode . '/title');
            $paymentMethods[] = array(
                'code' => $paymentCode,
                'title' => $paymentTitle ? $paymentTitle : $paymentCode
            );
        }
        
        return $paymentMethods;
    }

    /**
     * Get shipping methods information
     * 
     * @return array Shipping methods data
     */
    public function getShippingMethods()
    {
        $shippingMethods = array();
        
        // Get all available shipping methods
        $methods = Mage::getSingleton('shipping/config')->getActiveCarriers();
        
        foreach ($methods as $shippingCode => $shippingModel) {
            $shippingTitle = Mage::getStoreConfig('carriers/' . $shippingCode . '/title');
            $shippingMethods[] = array(
                'code' => $shippingCode,
                'title' => $shippingTitle ? $shippingTitle : $shippingCode
            );
        }
        
        return $shippingMethods;
    }

    /**
     * Format number for display
     * 
     * @param int|float $number Number to format
     * @return string Formatted number
     */
    public function formatNumber($number)
    {
        return Mage::helper('vipwatches_analytics')->formatNumber($number);
    }
}
